<div class="profile-container">
  <!-- Profile Header Moderne -->
  <div class="profile-header">
    <div class="header-background"></div>

    <div class="profile-main-info">
      <div class="avatar-section">
        <label for="fileInput" class="avatar-container">
          <img [src]="avatarUrl" class="avatar-icon" alt="Profile Picture" (error)="onImageError($event)" />
          <div class="avatar-overlay">
            <mat-icon>photo_camera</mat-icon>
          </div>
          <input type="file" id="fileInput" (change)="onFileSelected($event)" hidden>
        </label>
      </div>

      <div class="user-main-details">
        <h1>{{ user?.firstName }} {{ user?.lastName }}</h1>
        <p class="user-title">{{ userProfile?.title || 'Candidat' }}</p>

        <div class="profile-actions" *ngIf="!isViewingOtherProfile">
          <button mat-raised-button class="edit-profile-btn" (click)="editProfile()">
            <mat-icon>edit</mat-icon> Éditer le profil
          </button>
          <button mat-stroked-button class="cv-btn" (click)="addCV()">
            <mat-icon>description</mat-icon> {{ userProfile?.cv?.url ? 'Voir CV' : 'Ajouter CV' }}
          </button>
          <button mat-stroked-button class="complete-profile-btn" (click)="openCompleteProfileModal()">
            <mat-icon>assignment_turned_in</mat-icon> Compléter
          </button>
        </div>

        <!-- Indicateur pour profil d'un autre utilisateur -->
        <div class="profile-viewer-info" *ngIf="isViewingOtherProfile">
          <mat-icon>visibility</mat-icon>
          <span>Profil de {{ user?.firstName }} {{ user?.lastName }}</span>
        </div>
      </div>
    </div>

    <div class="profile-tabs">
      <div class="contact-info">
        <div class="contact-item">
          <mat-icon>email</mat-icon>
          <span>{{ user?.email }}</span>
        </div>
        <div class="contact-item">
          <mat-icon>phone</mat-icon>
          <span>{{ user?.phoneNumber || 'Non renseigné' }}</span>
        </div>
        <div class="contact-item">
          <mat-icon>location_on</mat-icon>
          <span>{{ user?.address || 'Non renseigné' }}</span>
        </div>
        <div class="contact-item">
          <mat-icon>cake</mat-icon>
          <span>{{ user?.dateOfBirth ? (user?.dateOfBirth | date:'dd/MM/yyyy') : 'Non renseigné' }}</span>
        </div>
      </div>

      <!-- Bio Section Ultra-Moderne -->
      <div class="bio-capsule" [ngClass]="{'editing': isEditingBio}">
        <!-- Mode affichage -->
        <div class="bio-preview" *ngIf="!isEditingBio">
          <div class="bio-label">
            <span>BIO</span>
          </div>

          <div class="bio-text-container" *ngIf="user?.bio" [class.clickable]="!isViewingOtherProfile" (click)="!isViewingOtherProfile && startEditingBio()">
            <div class="bio-text">{{ user?.bio }}</div>
            <div class="bio-edit-hint" *ngIf="!isViewingOtherProfile">
              <mat-icon>edit</mat-icon>
            </div>
          </div>

          <div class="bio-empty" *ngIf="!user?.bio && !isViewingOtherProfile" (click)="startEditingBio()">
            <div class="empty-message">
              <span class="primary-message">Partagez votre parcours professionnel</span>
              <span class="secondary-message">Cliquez ici pour ajouter votre bio</span>
            </div>
            <mat-icon>add_circle_outline</mat-icon>
          </div>

          <div class="bio-empty" *ngIf="!user?.bio && isViewingOtherProfile">
            <div class="empty-message">
              <span class="primary-message">Aucune bio disponible</span>
            </div>
          </div>
        </div>

        <!-- Mode édition -->
        <div class="bio-editor" *ngIf="isEditingBio">
          <div class="editor-container">
            <textarea
              [(ngModel)]="tempBio"
              placeholder="Décrivez votre parcours en quelques mots..."
              rows="2"
              #bioTextarea
              (keydown.escape)="cancelEditingBio()"
              (keydown.control.enter)="saveBio()"
            ></textarea>

            <div class="editor-actions">
              <div class="editor-shortcuts">
                <span>Ctrl+Enter: Enregistrer</span>
                <span>Esc: Annuler</span>
              </div>

              <div class="editor-buttons">
                <button mat-icon-button class="cancel-btn" (click)="cancelEditingBio()" [disabled]="isSavingBio" matTooltip="Annuler">
                  <mat-icon>close</mat-icon>
                </button>
                <button mat-icon-button class="save-btn" (click)="saveBio()" [disabled]="isSavingBio" matTooltip="Enregistrer">
                  <mat-icon *ngIf="!isSavingBio">check</mat-icon>
                  <mat-icon *ngIf="isSavingBio" class="spinner">autorenew</mat-icon>
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>

  <mat-grid-list cols="6" rowHeight="1.4:1">

  <!-- Left Column -->
  <mat-grid-tile colspan="2" rowspan="5">
    <div class="left-column">
      <!-- Personal Information -->
      <mat-card class="info-card">
        <mat-card-header>
          <mat-card-title>
            <mat-icon class="card-icon">person</mat-icon>
            Informations personnelles
          </mat-card-title>
        </mat-card-header>
        <mat-card-content>
          <p><mat-icon>cake</mat-icon> Date de naissance: {{ user?.dateOfBirth ? (user?.dateOfBirth | date) : 'Non renseigné' }}</p>
          <p><mat-icon>email</mat-icon> Email: {{ user?.email }}</p>
          <p><mat-icon>phone</mat-icon> Téléphone: {{ user?.phoneNumber || 'Non renseigné' }}</p>
          <p><mat-icon>location_on</mat-icon> Adresse: {{ user?.address || 'Non renseigné' }}</p>
        </mat-card-content>
      </mat-card>

      <!-- Missing Details -->
      <mat-card class="info-card highlight-card">
        <mat-card-header>
          <mat-card-title>
            <mat-icon class="card-icon">assignment_late</mat-icon>
            Complétez votre profil
          </mat-card-title>
        </mat-card-header>
        <mat-card-content>
          Ajoutez vos détails manquants pour un profil complet.
          <button mat-raised-button class="add-now-btn" (click)="openCompleteProfileModal()">Ajouter maintenant →</button>
        </mat-card-content>
      </mat-card>


    </div>
  </mat-grid-tile>

  <!-- Right Column -->
  <mat-grid-tile colspan="4" rowspan="9">
    <div class="right-column">
      <!-- Certifications -->
      <mat-card class="info-card">
        <mat-card-header>
          <mat-card-title>
            <mat-icon class="card-icon">verified</mat-icon>
            Certifications
          </mat-card-title>
        </mat-card-header>
        <mat-card-content>
          <ng-container *ngIf="userProfile?.certifications?.length > 0; else noCertifications">
            <div *ngIf="!showAllCertifications">
              <div class="item-block">
                <div class="item-header">
                  <div class="item-title">
                    <h4>{{ userProfile.certifications[0]?.name || userProfile.certifications[0]?.nom || 'Certification sans nom' }}</h4>
                    <span class="item-subtitle">{{ userProfile.certifications[0]?.issuingOrganization || 'Organisation non spécifiée' }}</span>
                  </div>
                  <div class="item-actions" *ngIf="!isViewingOtherProfile">
                    <button mat-icon-button class="action-btn" (click)="editCertification(userProfile.certifications[0], 0)">
                      <mat-icon>edit</mat-icon>
                    </button>
                    <button mat-icon-button class="action-btn" (click)="deleteCertification(0)">
                      <mat-icon>delete</mat-icon>
                    </button>
                  </div>
                </div>
                <div class="item-details">
                  <p>{{ userProfile.certifications[0]?.issueDate ? (userProfile.certifications[0]?.issueDate | date) : 'Non renseigné' }} -
                    {{ userProfile.certifications[0]?.expirationDate ? (userProfile.certifications[0]?.expirationDate | date) : 'Pas d\'expiration' }}</p>
                </div>
              </div>
            </div>
            <div *ngIf="showAllCertifications">
              <div class="item-block" *ngFor="let certification of userProfile.certifications; let i = index">
                <div class="item-header">
                  <div class="item-title">
                    <h4>{{ certification.name || certification.nom || 'Certification sans nom' }}</h4>
                    <span class="item-subtitle">{{ certification.issuingOrganization || 'Organisation non spécifiée' }}</span>
                  </div>
                  <div class="item-actions">
                    <button mat-icon-button class="action-btn" (click)="editCertification(certification, i)">
                      <mat-icon>edit</mat-icon>
                    </button>
                    <button mat-icon-button class="action-btn" (click)="deleteCertification(i)">
                      <mat-icon>delete</mat-icon>
                    </button>
                  </div>
                </div>
                <div class="item-details">
                  <p>{{ certification.issueDate ? (certification.issueDate | date) : 'Non renseigné' }} -
                    {{ certification.expirationDate ? (certification.expirationDate | date) : 'Pas d\'expiration' }}</p>
                </div>
              </div>
            </div>
            <div *ngIf="userProfile.certifications.length > 1" class="show-more">
              <button mat-button (click)="toggleCertifications()" [disabled]="isLoading">
                {{ showAllCertifications ? 'Masquer' : 'Afficher les ' + (userProfile.certifications.length - 1) + ' autres' }}
              </button>
            </div>
          </ng-container>
          <ng-template #noCertifications>
            <p class="empty-message">Aucune certification ajoutée pour le moment.</p>
          </ng-template>
          <div class="info-row" *ngIf="!isViewingOtherProfile">
            <span>Ajouter une certification</span>
            <button mat-button class="add-btn" (click)="addCertification()">Ajouter +</button>
          </div>
        </mat-card-content>
      </mat-card>

      <!-- Work Experience -->
      <mat-card class="info-card">
        <mat-card-header>
          <mat-card-title>
            <mat-icon class="card-icon">work</mat-icon>
            Expérience professionnelle
          </mat-card-title>
        </mat-card-header>
        <mat-card-content>
          <ng-container *ngIf="userProfile?.experiences?.length > 0; else noExperience">
            <div *ngIf="!showAllExperiences">
              <div class="item-block">
                <div class="item-header">
                  <div class="item-title">
                    <h4>{{ userProfile.experiences[0]?.title || 'Expérience sans titre' }}</h4>
                    <span class="item-subtitle">{{ userProfile.experiences[0]?.company || 'Entreprise non spécifiée' }}</span>
                  </div>
                  <div class="item-actions">
                    <button mat-icon-button class="action-btn" (click)="editExperience(userProfile.experiences[0], 0)">
                      <mat-icon>edit</mat-icon>
                    </button>
                    <button mat-icon-button class="action-btn" (click)="deleteExperience(0)">
                      <mat-icon>delete</mat-icon>
                    </button>
                  </div>
                </div>
                <div class="item-details">
                  <p>{{ userProfile.experiences[0]?.startDate ? (userProfile.experiences[0]?.startDate | date) : 'Non renseigné' }} -
                    {{ userProfile.experiences[0]?.endDate ? (userProfile.experiences[0]?.endDate | date) : 'Aujourd\'hui' }}</p>
                </div>
              </div>
            </div>
            <div *ngIf="showAllExperiences">
              <div class="item-block" *ngFor="let experience of userProfile.experiences; let i = index">
                <div class="item-header">
                  <div class="item-title">
                    <h4>{{ experience.title || 'Expérience sans titre' }}</h4>
                    <span class="item-subtitle">{{ experience.company || 'Entreprise non spécifiée' }}</span>
                  </div>
                  <div class="item-actions">
                    <button mat-icon-button class="action-btn" (click)="editExperience(experience, i)">
                      <mat-icon>edit</mat-icon>
                    </button>
                    <button mat-icon-button class="action-btn" (click)="deleteExperience(i)">
                      <mat-icon>delete</mat-icon>
                    </button>
                  </div>
                </div>
                <div class="item-details">
                  <p>{{ experience.startDate ? (experience.startDate | date) : 'Non renseigné' }} -
                    {{ experience.endDate ? (experience.endDate | date) : 'Aujourd\'hui' }}</p>
                </div>
              </div>
            </div>
            <div *ngIf="userProfile.experiences.length > 1" class="show-more">
              <button mat-button (click)="toggleExperiences()" [disabled]="isLoading">
                {{ showAllExperiences ? 'Masquer' : 'Afficher les ' + (userProfile.experiences.length - 1) + ' autres' }}
              </button>
            </div>
          </ng-container>
          <ng-template #noExperience>
            <p class="empty-message">Aucune expérience ajoutée pour le moment.</p>
          </ng-template>
          <div class="info-row">
            <span>Ajouter une expérience</span>
            <button mat-button class="add-btn" (click)="addExperience()">Ajouter +</button>
          </div>
        </mat-card-content>
      </mat-card>

      <!-- Education -->
      <mat-card class="info-card">
        <mat-card-header>
          <mat-card-title>
            <mat-icon class="card-icon">school</mat-icon>
            Formation
          </mat-card-title>
        </mat-card-header>
        <mat-card-content>
          <ng-container *ngIf="userProfile?.educations?.length > 0; else noEducation">
            <div *ngIf="!showAllEducations">
              <div class="item-block">
                <div class="item-header">
                  <div class="item-title">
                    <h4>{{ userProfile.educations[0]?.school || 'École non spécifiée' }}</h4>
                    <span class="item-subtitle">{{ userProfile.educations[0]?.degree }} - {{ userProfile.educations[0]?.fieldOfStudy }}</span>
                  </div>
                  <div class="item-actions">
                    <button mat-icon-button class="action-btn" (click)="editEducation(userProfile.educations[0], 0)">
                      <mat-icon>edit</mat-icon>
                    </button>
                    <button mat-icon-button class="action-btn" (click)="deleteEducation(0)">
                      <mat-icon>delete</mat-icon>
                    </button>
                  </div>
                </div>
                <div class="item-details">
                  <p>{{ userProfile.educations[0]?.startDate ? (userProfile.educations[0]?.startDate | date) : 'Non renseigné' }} -
                    {{ userProfile.educations[0]?.endDate ? (userProfile.educations[0]?.endDate | date) : 'En cours' }}</p>
                </div>
              </div>
            </div>
            <div *ngIf="showAllEducations">
              <div class="item-block" *ngFor="let education of userProfile.educations; let i = index">
                <div class="item-header">
                  <div class="item-title">
                    <h4>{{ education.school || 'École non spécifiée' }}</h4>
                    <span class="item-subtitle">{{ education.degree }} - {{ education.fieldOfStudy }}</span>
                  </div>
                  <div class="item-actions">
                    <button mat-icon-button class="action-btn" (click)="editEducation(education, i)">
                      <mat-icon>edit</mat-icon>
                    </button>
                    <button mat-icon-button class="action-btn" (click)="deleteEducation(i)">
                      <mat-icon>delete</mat-icon>
                    </button>
                  </div>
                </div>
                <div class="item-details">
                  <p>{{ education.startDate ? (education.startDate | date) : 'Non renseigné' }} -
                    {{ education.endDate ? (education.endDate | date) : 'En cours' }}</p>
                </div>
              </div>
            </div>
            <div *ngIf="userProfile.educations.length > 1" class="show-more">
              <button mat-button (click)="toggleEducations()" [disabled]="isLoading">
                {{ showAllEducations ? 'Masquer' : 'Afficher les ' + (userProfile.educations.length - 1) + ' autres' }}
              </button>
            </div>
          </ng-container>
          <ng-template #noEducation>
            <p class="empty-message">Aucune formation ajoutée pour le moment.</p>
          </ng-template>
          <div class="info-row">
            <span>Ajouter une formation</span>
            <button mat-button class="add-btn" (click)="addEducation()">Ajouter +</button>
          </div>
        </mat-card-content>
      </mat-card>

      <!-- Skills -->
      <mat-card class="info-card">
        <mat-card-header>
          <mat-card-title>
            <mat-icon class="card-icon">psychology</mat-icon>
            Compétences
          </mat-card-title>
        </mat-card-header>
        <mat-card-content>
          <ng-container *ngIf="userProfile?.skills?.length > 0; else noSkills">
            <div *ngIf="!showAllSkills">
              <div class="item-block skill-item">
                <div class="item-header">
                  <div class="item-title">
                    <h4>{{ userProfile.skills[0]?.name || userProfile.skills[0]?.nom || userProfile.skills[0] || 'Compétence sans nom' }}</h4>
                    <span>{{ userProfile.skills[0]?.level || userProfile.skills[0]?.niveau || 'Niveau non spécifié' }}</span>
                  </div>
                  <div class="item-actions">
                    <button mat-icon-button class="action-btn" (click)="editSkill(userProfile.skills[0], 0)">
                      <mat-icon>edit</mat-icon>
                    </button>
                    <button mat-icon-button class="action-btn" (click)="deleteSkill(0)">
                      <mat-icon>delete</mat-icon>
                    </button>
                  </div>
                </div>
              </div>
            </div>
            <div *ngIf="showAllSkills">
              <div class="item-block skill-item" *ngFor="let skill of userProfile.skills; let i = index">
                <div class="item-header">
                  <div class="item-title">
                    <h4>{{ skill.name || skill.nom || skill || 'Compétence sans nom' }}</h4>
                    <span>{{ skill.level || skill.niveau || 'Niveau non spécifié' }}</span>
                  </div>
                  <div class="item-actions">
                    <button mat-icon-button class="action-btn" (click)="editSkill(skill, i)">
                      <mat-icon>edit</mat-icon>
                    </button>
                    <button mat-icon-button class="action-btn" (click)="deleteSkill(i)">
                      <mat-icon>delete</mat-icon>
                    </button>
                  </div>
                </div>
              </div>
            </div>
            <div *ngIf="userProfile.skills.length > 1" class="show-more">
              <button mat-button (click)="toggleSkills()" [disabled]="isLoading">
                {{ showAllSkills ? 'Masquer' : 'Afficher les ' + (userProfile.skills.length - 1) + ' autres' }}
              </button>
            </div>
          </ng-container>
          <ng-template #noSkills>
            <p class="empty-message">Aucune compétence ajoutée pour le moment.</p>
          </ng-template>
          <div class="info-row">
            <span>Ajouter une compétence</span>
            <button mat-button class="add-btn" (click)="addSkill()">Ajouter +</button>
          </div>
        </mat-card-content>
      </mat-card>

      <!-- Links -->
      <mat-card class="info-card">
        <mat-card-header>
          <mat-card-title>
            <mat-icon class="card-icon">link</mat-icon>
            Liens professionnels
          </mat-card-title>
        </mat-card-header>
        <mat-card-content>
          <ng-container *ngIf="userProfile?.links?.length > 0; else noLinks">
            <div *ngIf="!showAllLinks">
              <div class="item-block">
                <div class="item-header">
                  <div class="item-title">
                    <a href="{{ userProfile.links[0]?.url }}" target="_blank" class="link-title">{{ userProfile.links[0]?.url }}</a>
                  </div>
                  <div class="item-actions">
                    <button mat-icon-button class="action-btn" (click)="editLink(userProfile.links[0], 0)">
                      <mat-icon>edit</mat-icon>
                    </button>
                    <button mat-icon-button class="action-btn" (click)="deleteLink(0)">
                      <mat-icon>delete</mat-icon>
                    </button>
                  </div>
                </div>
              </div>
            </div>
            <div *ngIf="showAllLinks">
              <div class="item-block" *ngFor="let link of userProfile.links; let i = index">
                <div class="item-header">
                  <div class="item-title">
                    <a href="{{ link.url }}" target="_blank" class="link-title">{{ link.url }}</a>
                  </div>
                  <div class="item-actions">
                    <button mat-icon-button class="action-btn" (click)="editLink(link, i)">
                      <mat-icon>edit</mat-icon>
                    </button>
                    <button mat-icon-button class="action-btn" (click)="deleteLink(i)">
                      <mat-icon>delete</mat-icon>
                    </button>
                  </div>
                </div>
              </div>
            </div>
            <div *ngIf="userProfile.links.length > 1" class="show-more">
              <button mat-button (click)="toggleLinks()" [disabled]="isLoading">
                {{ showAllLinks ? 'Masquer' : 'Afficher les ' + (userProfile.links.length - 1) + ' autres' }}
              </button>
            </div>
          </ng-container>
          <ng-template #noLinks>
            <p class="empty-message">Aucun lien ajouté pour le moment.</p>
          </ng-template>
          <div class="info-row">
            <span>Ajouter un lien</span>
            <button mat-button class="add-btn" (click)="addLink()">Ajouter +</button>
          </div>
        </mat-card-content>
      </mat-card>
    </div>
  </mat-grid-tile>
</mat-grid-list>
</div>
