<div class="workspace-posts-container">
  <!-- Header avec statistiques -->
  <div class="posts-header">
    <div class="header-content">
      <div class="title-section">
        <h1 class="page-title">
          <mat-icon class="title-icon">article</mat-icon>
          Vos Posts
        </h1>
        <p class="workspace-name">{{ workspaceName }}</p>
      </div>
      
      <div class="stats-cards" *ngIf="!isLoading">
        <div class="stat-card total">
          <div class="stat-number">{{ totalPosts }}</div>
          <div class="stat-label">Total</div>
        </div>
        <div class="stat-card active">
          <div class="stat-number">{{ activePosts }}</div>
          <div class="stat-label">Actifs</div>
        </div>
        <div class="stat-card archived">
          <div class="stat-number">{{ archivedPosts }}</div>
          <div class="stat-label">Archivés</div>
        </div>
      </div>
    </div>
  </div>

  <!-- Barre de recherche et actions -->
  <div class="toolbar" *ngIf="!isLoading">
    <mat-form-field class="search-field" appearance="outline">
      <mat-label>Rechercher un post...</mat-label>
      <input matInput 
             [(ngModel)]="searchTerm" 
             (input)="filterPosts()"
             placeholder="Titre, description, entreprise...">
      <mat-icon matSuffix>search</mat-icon>
    </mat-form-field>
    
    <button mat-raised-button 
            color="primary" 
            class="create-btn"
            (click)="createNewPost()">
      <mat-icon>add</mat-icon>
      Nouveau Post
    </button>
  </div>

  <!-- Loading -->
  <div *ngIf="isLoading" class="loading-container">
    <mat-spinner></mat-spinner>
    <p>Chargement de vos posts...</p>
  </div>

  <!-- Aucun post -->
  <div *ngIf="!isLoading && posts.length === 0" class="no-posts">
    <mat-icon class="no-posts-icon">article</mat-icon>
    <h3>Aucun post trouvé</h3>
    <p>Vous n'avez pas encore publié de posts dans ce workspace.</p>
    <button mat-raised-button color="primary" (click)="createNewPost()">
      <mat-icon>add</mat-icon>
      Créer votre premier post
    </button>
  </div>

  <!-- Liste des posts -->
  <div *ngIf="!isLoading && posts.length > 0" class="posts-content">
    <div class="posts-grid">
      <div *ngFor="let post of getPaginatedPosts()" class="post-card">
        <!-- En-tête de la carte -->
        <div class="card-header">
          <div class="post-info">
            <h3 class="post-title" (click)="viewPost(post)">
              {{ post.titre }}
            </h3>
            <p class="post-company">{{ post.entreprise }}</p>
          </div>
          <mat-chip [class]="getStatusClass(post)">
            {{ getStatusLabel(post) }}
          </mat-chip>
        </div>

        <!-- Description -->
        <div class="post-description">
          <p>{{ post.description | slice:0:150 }}{{ post.description.length > 150 ? '...' : '' }}</p>
        </div>

        <!-- Détails du poste -->
        <div class="post-details">
          <div class="detail-item">
            <mat-icon class="detail-icon">work</mat-icon>
            <span>{{ post.contractType }}</span>
          </div>
          
          <div class="detail-item">
            <mat-icon class="detail-icon">person</mat-icon>
            <span>{{ post.profileRequest | slice:0:50 }}{{ post.profileRequest.length > 50 ? '...' : '' }}</span>
          </div>
          
          <div class="detail-item">
            <mat-icon class="detail-icon">event</mat-icon>
            <span>{{ post.datePublication | date: 'dd/MM/yyyy' }}</span>
          </div>
        </div>

        <!-- Actions -->
        <div class="card-actions">
          <button mat-button class="action-btn view-btn" (click)="viewPost(post)">
            <mat-icon>visibility</mat-icon>
            Voir
          </button>
          
          <button mat-button class="action-btn edit-btn" (click)="editPost(post)">
            <mat-icon>edit</mat-icon>
            Modifier
          </button>
          
          <button mat-button class="action-btn candidates-btn" (click)="viewCandidates(post)">
            <mat-icon>people</mat-icon>
            Candidats
          </button>
          
          <button *ngIf="!post.archived" 
                  mat-button 
                  class="action-btn archive-btn" 
                  (click)="archivePost(post)">
            <mat-icon>archive</mat-icon>
            Archiver
          </button>
          
          <button *ngIf="post.archived" 
                  mat-button 
                  class="action-btn unarchive-btn" 
                  (click)="unarchivePost(post)">
            <mat-icon>unarchive</mat-icon>
            Désarchiver
          </button>
        </div>
      </div>
    </div>

    <!-- Pagination -->
    <mat-paginator 
      [length]="length"
      [pageSize]="pageSize"
      [pageIndex]="pageIndex"
      [pageSizeOptions]="[5, 10, 20, 50]"
      (page)="onPageChange($event)"
      showFirstLastButtons>
    </mat-paginator>
  </div>
</div>
