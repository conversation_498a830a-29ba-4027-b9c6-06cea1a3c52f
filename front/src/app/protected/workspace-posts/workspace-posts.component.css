/* Workspace Posts - ADNIA Style */
.workspace-posts-container {
  padding: 30px;
  background: #f8f9fa;
  min-height: 100vh;
}

/* Header */
.posts-header {
  background: white;
  border-radius: 8px;
  padding: 25px;
  margin-bottom: 25px;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.header-content {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  gap: 30px;
}

.title-section {
  flex: 1;
}

.page-title {
  display: flex;
  align-items: center;
  gap: 12px;
  font-size: 24px;
  font-weight: 600;
  color: #2c3e50;
  margin: 0 0 8px 0;
}

.title-icon {
  font-size: 28px;
  color: #1abc9c;
}

.workspace-name {
  font-size: 14px;
  color: #7f8c8d;
  margin: 0;
  font-weight: 500;
}

/* Stats Cards */
.stats-cards {
  display: flex;
  gap: 15px;
}

.stat-card {
  background: white;
  border-radius: 8px;
  padding: 20px;
  text-align: center;
  min-width: 80px;
  border-left: 4px solid #1abc9c;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.stat-card.total {
  border-left-color: #2c3e50;
}

.stat-card.active {
  border-left-color: #27ae60;
}

.stat-card.archived {
  border-left-color: #95a5a6;
}

.stat-number {
  font-size: 24px;
  font-weight: 700;
  margin-bottom: 4px;
}

.stat-card.total .stat-number {
  color: #2c3e50;
}

.stat-card.active .stat-number {
  color: #27ae60;
}

.stat-card.archived .stat-number {
  color: #95a5a6;
}

.stat-label {
  font-size: 12px;
  color: #7f8c8d;
  font-weight: 600;
  text-transform: uppercase;
}

/* Toolbar */
.toolbar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 25px;
  gap: 20px;
}

.search-field {
  flex: 1;
  max-width: 400px;
}

.create-btn {
  background: #1abc9c !important;
  color: white !important;
  font-weight: 600 !important;
  padding: 0 24px !important;
  height: 48px !important;
}

.create-btn:hover {
  background: #16a085 !important;
}

/* Loading */
.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 60px;
  text-align: center;
}

.loading-container p {
  margin-top: 16px;
  color: #7f8c8d;
  font-size: 16px;
}

/* No posts */
.no-posts {
  background: white;
  border-radius: 8px;
  padding: 60px;
  text-align: center;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.no-posts-icon {
  font-size: 64px;
  color: #bdc3c7;
  margin-bottom: 16px;
}

.no-posts h3 {
  color: #2c3e50;
  margin: 0 0 8px 0;
  font-size: 20px;
  font-weight: 600;
}

.no-posts p {
  color: #7f8c8d;
  margin: 0 0 24px 0;
  font-size: 16px;
}

/* Posts Grid */
.posts-content {
  background: white;
  border-radius: 8px;
  padding: 25px;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.posts-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(400px, 1fr));
  gap: 20px;
  margin-bottom: 30px;
}

/* Post Card */
.post-card {
  background: #f8f9fa;
  border-radius: 8px;
  padding: 20px;
  border: 1px solid #e9ecef;
  transition: all 0.3s ease;
}

.post-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0,0,0,0.1);
  border-color: #1abc9c;
}

/* Card Header */
.card-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 15px;
}

.post-info {
  flex: 1;
}

.post-title {
  font-size: 18px;
  font-weight: 600;
  color: #2c3e50;
  margin: 0 0 4px 0;
  cursor: pointer;
  transition: color 0.3s ease;
}

.post-title:hover {
  color: #1abc9c;
}

.post-company {
  font-size: 14px;
  color: #7f8c8d;
  margin: 0;
  font-weight: 500;
}

/* Status Chips */
.status-active {
  background-color: #d4edda !important;
  color: #155724 !important;
}

.status-archived {
  background-color: #f8d7da !important;
  color: #721c24 !important;
}

/* Post Description */
.post-description {
  margin-bottom: 15px;
}

.post-description p {
  color: #495057;
  line-height: 1.5;
  margin: 0;
}

/* Post Details */
.post-details {
  display: flex;
  flex-direction: column;
  gap: 8px;
  margin-bottom: 20px;
}

.detail-item {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 14px;
  color: #6c757d;
}

.detail-icon {
  font-size: 16px;
  color: #1abc9c;
  width: 16px;
}

/* Card Actions */
.card-actions {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
  padding-top: 15px;
  border-top: 1px solid #e9ecef;
}

.action-btn {
  font-size: 12px !important;
  padding: 8px 12px !important;
  min-width: auto !important;
  height: 32px !important;
}

.view-btn {
  color: #1abc9c !important;
}

.edit-btn {
  color: #3498db !important;
}

.candidates-btn {
  color: #9b59b6 !important;
}

.archive-btn {
  color: #e67e22 !important;
}

.unarchive-btn {
  color: #27ae60 !important;
}

.action-btn:hover {
  background-color: rgba(0,0,0,0.04) !important;
}

.action-btn mat-icon {
  margin-right: 4px;
  font-size: 16px;
}

/* Pagination */
mat-paginator {
  background: transparent !important;
  border-top: 1px solid #e9ecef;
  padding-top: 20px;
}

/* Responsive */
@media (max-width: 768px) {
  .workspace-posts-container {
    padding: 20px;
  }
  
  .header-content {
    flex-direction: column;
    gap: 20px;
  }
  
  .stats-cards {
    flex-wrap: wrap;
    justify-content: center;
  }
  
  .toolbar {
    flex-direction: column;
    align-items: stretch;
  }
  
  .posts-grid {
    grid-template-columns: 1fr;
  }
  
  .card-actions {
    flex-direction: column;
  }
}
