import { Component, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { MatCardModule } from '@angular/material/card';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { MatChipsModule } from '@angular/material/chips';
import { MatProgressSpinnerModule } from '@angular/material/progress-spinner';
import { MatPaginatorModule, PageEvent } from '@angular/material/paginator';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatInputModule } from '@angular/material/input';
import { FormsModule } from '@angular/forms';
import { Router } from '@angular/router';
import { WorkspaceService } from '../../services/workspace/workspace.service';
import { PostsService } from '../../services/posts/posts.service';
import { UserService } from '../../services/user/user.service';

interface Post {
  id: string;
  titre: string;
  description: string;
  entreprise: string;
  contractType: string;
  profileRequest: string;
  datePublication: string;
  archived: boolean;
  userId: string;
}

@Component({
  selector: 'app-workspace-posts',
  standalone: true,
  imports: [
    CommonModule,
    MatCardModule,
    MatButtonModule,
    MatIconModule,
    MatChipsModule,
    MatProgressSpinnerModule,
    MatPaginatorModule,
    MatFormFieldModule,
    MatInputModule,
    FormsModule
  ],
  templateUrl: './workspace-posts.component.html',
  styleUrl: './workspace-posts.component.css'
})
export class WorkspacePostsComponent implements OnInit {
  posts: Post[] = [];
  filteredPosts: Post[] = [];
  isLoading = true;
  workspaceName = '';
  currentUserId = '';
  searchTerm = '';
  
  // Pagination
  pageIndex = 0;
  pageSize = 10;
  length = 0;

  // Stats
  totalPosts = 0;
  activePosts = 0;
  archivedPosts = 0;

  constructor(
    private workspaceService: WorkspaceService,
    private postsService: PostsService,
    private userService: UserService,
    private router: Router
  ) {}

  ngOnInit() {
    this.loadCurrentUser();
  }

  loadCurrentUser() {
    this.userService.getUserProfile().subscribe({
      next: (user) => {
        this.currentUserId = user.id;
        this.loadWorkspacePosts();
      },
      error: (error) => {
        console.error('Erreur lors du chargement du profil utilisateur:', error);
        this.isLoading = false;
      }
    });
  }

  loadWorkspacePosts() {
    this.isLoading = true;
    
    // Récupérer l'ID du workspace actif
    const activeWorkspaceId = this.workspaceService.getActiveWorkspaceId();
    
    if (activeWorkspaceId) {
      // Récupérer les informations du workspace
      this.workspaceService.getWorkspaceById(activeWorkspaceId).subscribe({
        next: (workspace) => {
          this.workspaceName = workspace.name;
          
          // Récupérer tous les posts du workspace
          this.workspaceService.getWorkspacePosts(activeWorkspaceId).subscribe({
            next: (allPosts: Post[]) => {
              // Filtrer pour ne garder que les posts du recruteur connecté
              this.posts = allPosts.filter(post => post.userId === this.currentUserId);
              this.filteredPosts = [...this.posts];
              this.calculateStats();
              this.updatePagination();
              this.isLoading = false;
            },
            error: (error) => {
              console.error('Erreur lors du chargement des posts du workspace:', error);
              this.loadMyPostsFallback();
            }
          });
        },
        error: (error) => {
          console.error('Erreur lors du chargement du workspace:', error);
          this.workspaceName = 'Workspace';
          this.loadMyPostsFallback();
        }
      });
    } else {
      console.error('Aucun workspace actif trouvé');
      this.workspaceName = 'Aucun workspace actif';
      this.loadMyPostsFallback();
    }
  }

  loadMyPostsFallback() {
    // Fallback: utiliser getMyPosts si getWorkspacePosts échoue
    this.postsService.getMyPosts(0, 100).subscribe({
      next: (response: any) => {
        this.posts = response.content || response;
        this.filteredPosts = [...this.posts];
        this.calculateStats();
        this.updatePagination();
        this.isLoading = false;
      },
      error: (error) => {
        console.error('Erreur lors du chargement des posts:', error);
        this.isLoading = false;
      }
    });
  }

  calculateStats() {
    this.totalPosts = this.posts.length;
    this.activePosts = this.posts.filter(post => !post.archived).length;
    this.archivedPosts = this.posts.filter(post => post.archived).length;
  }

  updatePagination() {
    this.length = this.filteredPosts.length;
    // Réinitialiser à la première page si nécessaire
    if (this.pageIndex * this.pageSize >= this.length) {
      this.pageIndex = 0;
    }
  }

  getPaginatedPosts(): Post[] {
    const startIndex = this.pageIndex * this.pageSize;
    const endIndex = startIndex + this.pageSize;
    return this.filteredPosts.slice(startIndex, endIndex);
  }

  onPageChange(event: PageEvent) {
    this.pageIndex = event.pageIndex;
    this.pageSize = event.pageSize;
  }

  filterPosts() {
    if (!this.searchTerm.trim()) {
      this.filteredPosts = [...this.posts];
    } else {
      this.filteredPosts = this.posts.filter(post =>
        post.titre.toLowerCase().includes(this.searchTerm.toLowerCase()) ||
        post.description.toLowerCase().includes(this.searchTerm.toLowerCase()) ||
        post.entreprise.toLowerCase().includes(this.searchTerm.toLowerCase())
      );
    }
    this.updatePagination();
  }

  getStatusLabel(post: Post): string {
    return post.archived ? 'Archivé' : 'Actif';
  }

  getStatusClass(post: Post): string {
    return post.archived ? 'status-archived' : 'status-active';
  }

  viewPost(post: Post) {
    this.router.navigate(['/post-detail', post.id, post.titre]);
  }

  editPost(post: Post) {
    this.router.navigate(['/edit-post', post.id]);
  }

  archivePost(post: Post) {
    this.postsService.archivePost(post.id).subscribe({
      next: () => {
        post.archived = true;
        this.calculateStats();
      },
      error: (error) => {
        console.error('Erreur lors de l\'archivage:', error);
      }
    });
  }

  unarchivePost(post: Post) {
    this.postsService.unarchivePost(post.id).subscribe({
      next: () => {
        post.archived = false;
        this.calculateStats();
      },
      error: (error) => {
        console.error('Erreur lors de la désarchivage:', error);
      }
    });
  }

  createNewPost() {
    this.router.navigate(['/posts']);
  }

  viewCandidates(post: Post) {
    this.router.navigate(['/candidat', post.id]);
  }
}
