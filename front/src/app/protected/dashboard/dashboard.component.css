@import url('https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;600;700&display=swap');

:root {
  --primary-purple: #5b21b6;
  --secondary-purple: #7c3aed;
  --accent-gold: #facc15;
  --background-dark: #1f2937;
  --background-light: #f3f4f6;
  --card-bg: #ffffff;
  --text-primary: #1f2937;
  --text-secondary: #6b7280;
  --border: #e5e7eb;
  --shadow-sm: 0 2px 6px rgba(0, 0, 0, 0.06);
  --shadow-md: 0 6px 16px rgba(0, 0, 0, 0.1);
  --shadow-lg: 0 12px 24px rgba(0, 0, 0, 0.12);
  --transition: all 0.3s cubic-bezier(0.25, 0.8, 0.25, 1);
}

* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  font-family: 'Poppins', sans-serif;
  background: var(--background-light);
}

.dashboard {
  display: grid;
  grid-template-columns: 260px 1fr;
  grid-template-areas: "sidebar main";
  min-height: 100vh;
  background: #f8f9fa;
}

/* Sidebar */
app-sidebar-recruiter {
  grid-area: sidebar;
  background: var(--primary-purple);
  color: #ffffff;
  padding: 28px;
  position: sticky;
  top: 0;
  height: 100vh;
  box-shadow: var(--shadow-sm);
}

/* Main content - ADNIA Style */
.dashboard-main {
  grid-area: main;
  padding: 30px;
  background: transparent;
}

/* Header - ADNIA Style */
.dashboard-header {
  background: white;
  padding: 25px 0;
  margin-bottom: 30px;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.header-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0 30px;
}

.header-left {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.header-right {
  display: flex;
  align-items: center;
  gap: 20px;
}

.dashboard-title {
  font-size: 24px;
  font-weight: 600;
  color: #2c3e50;
  margin: 0;
}

.title-icon {
  display: none;
}

.dashboard-subtitle {
  font-size: 14px;
  font-weight: 400;
  color: #7f8c8d;
  margin: 0;
}

/* Search container */
.search-container {
  display: flex;
  align-items: center;
  background: var(--background-light);
  border: 1px solid var(--border);
  border-radius: 10px;
  padding: 10px 18px;
  transition: var(--transition);
  width: 380px;
}

.search-container:focus-within {
  border-color: var(--accent-gold);
  box-shadow: 0 0 0 3px rgba(250, 204, 21, 0.15);
}

.search-icon {
  color: var(--text-secondary);
  font-size: 22px;
  margin-right: 10px;
}

.search-container:focus-within .search-icon {
  color: var(--accent-gold);
}

.search-bar {
  border: none;
  background: transparent;
  flex: 1;
  font-size: 14px;
  color: var(--text-primary);
  outline: none;
}

.search-bar::placeholder {
  color: var(--text-secondary);
}

/* Profile button */
.profile-button {
  display: flex;
  align-items: center;
  gap: 8px;
  background: var(--secondary-purple);
  color: #ffffff;
  border-radius: 10px;
  padding: 10px 18px;
  font-size: 14px;
  font-weight: 600;
  border: none;
  cursor: pointer;
  transition: var(--transition);
}

.profile-button:hover {
  background: var(--primary-purple);
  transform: translateY(-2px);
  box-shadow: var(--shadow-sm);
}

.profile-button mat-icon {
  font-size: 22px;
}

/* Control Buttons */
.notification-button,
.refresh-button,
.export-button {
  width: 44px;
  height: 44px;
  background: var(--card-bg);
  border: 1px solid var(--border);
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: var(--text-secondary);
  transition: var(--transition);
}

.notification-button:hover,
.refresh-button:hover,
.export-button:hover {
  background: var(--accent-gold);
  color: var(--text-primary);
  border-color: transparent;
}

.notification-badge {
  position: absolute;
  top: -8px;
  right: -8px;
  background: var(--accent-gold);
  color: var(--text-primary);
  border-radius: 50%;
  width: 22px;
  height: 22px;
  font-size: 12px;
  font-weight: 700;
  display: flex;
  align-items: center;
  justify-content: center;
  animation: pulse 2.5s infinite ease-in-out;
}

@keyframes pulse {
  0% { transform: scale(1); }
  50% { transform: scale(1.2); }
  100% { transform: scale(1); }
}

.spinning {
  animation: spin 1.2s linear infinite;
}

@keyframes spin {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

/* Notifications Panel */
.notifications-panel {
  position: absolute;
  top: 90px;
  right: 28px;
  width: 440px;
  max-height: 640px;
  background: var(--card-bg);
  border-radius: 20px;
  box-shadow: var(--shadow-lg);
  overflow: hidden;
  z-index: 1000;
}

.notifications-header {
  padding: 18px 24px;
  background: var(--primary-purple);
  color: #ffffff;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.notifications-header h3 {
  font-size: 20px;
  font-weight: 600;
}

.notifications-list {
  max-height: 540px;
  overflow-y: auto;
  padding: 18px;
}

.notification-item {
  display: flex;
  align-items: center;
  gap: 14px;
  padding: 14px;
  margin-bottom: 10px;
  background: var(--background-light);
  border-radius: 10px;
  transition: var(--transition);
}

.notification-item:hover {
  transform: translateY(-3px);
  box-shadow: var(--shadow-sm);
}

.notification-item.success { border-left: 5px solid #22c55e; }
.notification-item.info { border-left: 5px solid var(--accent-gold); }
.notification-item.warning { border-left: 5px solid #f59e0b; }
.notification-item.error { border-left: 5px solid #ef4444; }

.notification-content h4 {
  font-size: 16px;
  font-weight: 600;
  color: var(--text-primary);
  margin: 0 0 5px 0;
}

.notification-content p {
  font-size: 14px;
  color: var(--text-secondary);
  margin: 0;
}

.notification-time {
  font-size: 12px;
  color: var(--text-secondary);
  opacity: 0.8;
}

/* Card container */
.card-container {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(340px, 1fr));
  gap: 28px;
  padding: 28px 0;
}

/* Statistics Overview - ADNIA Style */
.stats-overview {
  margin-bottom: 30px;
}

.stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: 20px;
}

.stat-card {
  background: white;
  border-radius: 8px;
  padding: 25px;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
  transition: all 0.3s ease;
  border-left: 4px solid #1abc9c;
}

.stat-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0,0,0,0.15);
}

.stat-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 15px;
}

.stat-label {
  font-size: 14px;
  font-weight: 500;
  color: #7f8c8d;
  margin: 0;
}

.stat-value {
  font-size: 32px;
  font-weight: 700;
  color: #2c3e50;
  margin: 10px 0;
}

.stat-change {
  font-size: 12px;
  font-weight: 500;
  color: #1abc9c;
}

.stat-change.positive {
  color: #27ae60;
}

.stat-change.negative {
  color: #e74c3c;
}

/* Quick Actions */
.quick-actions {
  padding: 28px 0;
}

.section-title {
  font-size: 24px;
  font-weight: 700;
  color: var(--primary-purple);
  display: flex;
  align-items: center;
  gap: 10px;
  margin-bottom: 28px;
}

.section-title mat-icon {
  font-size: 28px;
  color: var(--accent-gold);
}

.actions-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(340px, 1fr));
  gap: 28px;
}

.action-card {
  background: var(--card-bg);
  border-radius: 20px;
  padding: 28px;
  display: flex;
  align-items: center;
  gap: 18px;
  box-shadow: var(--shadow-md);
  transition: var(--transition);
}

.action-card:hover {
  transform: translateY(-5px);
  box-shadow: var(--shadow-lg);
}

.action-icon {
  width: 60px;
  height: 60px;
  background: var(--accent-gold);
  border-radius: 14px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.action-icon mat-icon {
  font-size: 30px;
  color: var(--text-primary);
}

.action-content h3 {
  font-size: 20px;
  font-weight: 700;
  color: var(--primary-purple);
  margin: 0 0 5px 0;
}

.action-content p {
  font-size: 15px;
  color: var(--text-secondary);
}

.action-arrow {
  font-size: 20px;
  color: var(--text-secondary);
  transition: var(--transition);
}

.action-card:hover .action-arrow {
  color: var(--accent-gold);
  transform: translateX(8px);
}

/* Recent Activity */
.recent-activity {
  padding: 28px 0;
}

.activity-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(440px, 1fr));
  gap: 28px;
}

.activity-section {
  background: var(--card-bg);
  border-radius: 20px;
  padding: 28px;
  box-shadow: var(--shadow-md);
  transition: var(--transition);
}

.activity-section:hover {
  transform: translateY(-5px);
  box-shadow: var(--shadow-lg);
}

.activity-title {
  font-size: 22px;
  font-weight: 700;
  color: var(--primary-purple);
  display: flex;
  align-items: center;
  gap: 12px;
  margin-bottom: 24px;
}

.activity-title mat-icon {
  font-size: 28px;
  color: var(--accent-gold);
}

.activity-list {
  display: flex;
  flex-direction: column;
  gap: 18px;
}

.activity-item {
  display: flex;
  align-items: center;
  gap: 18px;
  padding: 18px;
  background: var(--background-light);
  border-radius: 12px;
  transition: var(--transition);
}

.activity-item:hover {
  transform: translateX(8px);
  background: rgba(250, 204, 21, 0.1);
}

.activity-icon {
  width: 52px;
  height: 52px;
  background: rgba(250, 204, 21, 0.15);
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.activity-icon mat-icon {
  font-size: 26px;
  color: var(--accent-gold);
}

.activity-details h4 {
  font-size: 18px;
  font-weight: 700;
  color: var(--text-primary);
}

.activity-details p {
  font-size: 15px;
  color: var(--text-secondary);
}

.activity-date {
  font-size: 13px;
  color: var(--text-secondary);
  opacity: 0.8;
}

.activity-status {
  padding: 6px 14px;
  border-radius: 10px;
  font-size: 14px;
  font-weight: 600;
}

.activity-status.active {
  background: rgba(34, 197, 94, 0.15);
  color: #22c55e;
}

.activity-score {
  padding: 6px 14px;
  border-radius: 10px;
  font-size: 14px;
  font-weight: 600;
  background: rgba(124, 58, 237, 0.15);
  color: var(--secondary-purple);
}

/* Loading */
.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 56px;
  color: var(--text-secondary);
}

.loading-spinner mat-icon {
  font-size: 40px;
  color: var(--accent-gold);
  animation: spin 1.3s linear infinite;
}

/* Responsive */
@media (max-width: 1200px) {
  .dashboard {
    grid-template-columns: 250px 1fr;
  }

  .search-container {
    width: 320px;
  }
}

@media (max-width: 992px) {
  .dashboard {
    grid-template-columns: 1fr;
    grid-template-areas: "main";
  }

  app-sidebar-recruiter {
    display: none;
  }

  .dashboard-main {
    margin: 12px;
    padding: 20px;
  }

  .header-content {
    flex-direction: column;
    gap: 16px;
    align-items: flex-start;
  }

  .search-container {
    width: 100%;
  }
}

@media (max-width: 768px) {
  .stats-grid,
  .actions-grid,
  .activity-grid {
    grid-template-columns: 1fr;
  }

  .dashboard-main {
    margin: 8px;
    padding: 16px;
    border-radius: 16px;
  }

  .dashboard-header {
    padding: 12px;
  }

  .stats-overview,
  .quick-actions,
  .recent-activity {
    padding: 12px 0;
  }
}
