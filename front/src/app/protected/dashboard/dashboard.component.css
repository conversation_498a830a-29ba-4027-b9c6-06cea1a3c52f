@import url('https://fonts.googleapis.com/css2?family=Roboto:wght@300;400;500;700&display=swap');

:root {
  --primary-blue: #1e3a8a;
  --secondary-blue: #2563eb;
  --accent-teal: #2dd4bf;
  --background-dark: #111827;
  --background-light: #f9fafb;
  --card-bg: #ffffff;
  --text-primary: #111827;
  --text-secondary: #6b7280;
  --border: #e5e7eb;
  --shadow-sm: 0 2px 4px rgba(0, 0, 0, 0.05);
  --shadow-md: 0 4px 12px rgba(0, 0, 0, 0.08);
  --shadow-lg: 0 10px 24px rgba(0, 0, 0, 0.1);
  --transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  font-family: 'Roboto', sans-serif;
  background: var(--background-light);
}

.dashboard {
  display: grid;
  grid-template-columns: 300px 1fr;
  grid-template-areas: "sidebar main";
  min-height: 100vh;
  background: linear-gradient(135deg, #f5f7fa 0%, #e4e7eb 100%);
}

/* Sidebar */
app-sidebar-recruiter {
  grid-area: sidebar;
  background: var(--background-dark);
  color: #ffffff;
  padding: 24px;
  position: sticky;
  top: 0;
  height: 100vh;
}

/* Main content */
.dashboard-main {
  grid-area: main;
  padding: 32px;
  margin: 24px;
  background: var(--card-bg);
  border-radius: 16px;
  box-shadow: var(--shadow-lg);
}

/* Header */
.dashboard-header {
  background: var(--card-bg);
  padding: 24px 32px;
  border-bottom: 1px solid var(--border);
  position: sticky;
  top: 0;
  z-index: 20;
  backdrop-filter: blur(10px);
}

.header-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
  max-width: 1400px;
  margin: 0 auto;
}

.header-left {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.header-right {
  display: flex;
  align-items: center;
  gap: 16px;
}

.dashboard-title {
  font-size: 28px;
  font-weight: 700;
  color: var(--primary-blue);
  display: flex;
  align-items: center;
  gap: 12px;
}

.title-icon {
  color: var(--accent-teal);
  font-size: 28px;
}

.dashboard-subtitle {
  font-size: 14px;
  font-weight: 400;
  color: var(--text-secondary);
}

/* Search container */
.search-container {
  display: flex;
  align-items: center;
  background: var(--background-light);
  border: 1px solid var(--border);
  border-radius: 12px;
  padding: 12px 20px;
  transition: var(--transition);
  width: 350px;
}

.search-container:focus-within {
  border-color: var(--accent-teal);
  box-shadow: 0 0 0 4px rgba(45, 212, 191, 0.1);
}

.search-icon {
  color: var(--text-secondary);
  font-size: 20px;
  margin-right: 12px;
}

.search-container:focus-within .search-icon {
  color: var(--accent-teal);
}

.search-bar {
  border: none;
  background: transparent;
  flex: 1;
  font-size: 15px;
  color: var(--text-primary);
  outline: none;
}

.search-bar::placeholder {
  color: var(--text-secondary);
}

/* Profile button */
.profile-button {
  display: flex;
  align-items: center;
  gap: 10px;
  background: var(--secondary-blue);
  color: #ffffff;
  border-radius: 12px;
  padding: 12px 20px;
  font-size: 15px;
  font-weight: 500;
  border: none;
  cursor: pointer;
  transition: var(--transition);
}

.profile-button:hover {
  background: var(--primary-blue);
  transform: translateY(-2px);
  box-shadow: var(--shadow-sm);
}

.profile-button mat-icon {
  font-size: 20px;
}

/* Control Buttons */
.notification-button,
.refresh-button,
.export-button {
  width: 40px;
  height: 40px;
  background: var(--card-bg);
  border: 1px solid var(--border);
  border-radius: 10px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: var(--text-secondary);
  transition: var(--transition);
}

.notification-button:hover,
.refresh-button:hover,
.export-button:hover {
  background: var(--accent-teal);
  color: #ffffff;
  border-color: transparent;
}

.notification-badge {
  position: absolute;
  top: -6px;
  right: -6px;
  background: var(--accent-teal);
  color: #ffffff;
  border-radius: 50%;
  width: 20px;
  height: 20px;
  font-size: 12px;
  font-weight: 600;
  display: flex;
  align-items: center;
  justify-content: center;
  animation: pulse 2s infinite ease-in-out;
}

@keyframes pulse {
  0% { transform: scale(1); }
  50% { transform: scale(1.15); }
  100% { transform: scale(1); }
}

.spinning {
  animation: spin 1s linear infinite;
}

@keyframes spin {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

/* Notifications Panel */
.notifications-panel {
  position: absolute;
  top: 80px;
  right: 32px;
  width: 420px;
  max-height: 600px;
  background: var(--card-bg);
  border-radius: 16px;
  box-shadow: var(--shadow-lg);
  overflow: hidden;
  z-index: 1000;
}

.notifications-header {
  padding: 20px 24px;
  background: var(--background-dark);
  color: #ffffff;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.notifications-header h3 {
  font-size: 18px;
  font-weight: 500;
}

.notifications-list {
  max-height: 500px;
  overflow-y: auto;
  padding: 16px;
}

.notification-item {
  display: flex;
  align-items: center;
  gap: 16px;
  padding: 16px;
  margin-bottom: 12px;
  background: var(--background-light);
  border-radius: 12px;
  transition: var(--transition);
}

.notification-item:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow-sm);
}

.notification-item.success { border-left: 4px solid #22c55e; }
.notification-item.info { border-left: 4px solid var(--accent-teal); }
.notification-item.warning { border-left: 4px solid #f59e0b; }
.notification-item.error { border-left: 4px solid #ef4444; }

.notification-content h4 {
  font-size: 15px;
  font-weight: 500;
  color: var(--text-primary);
  margin: 0 0 6px 0;
}

.notification-content p {
  font-size: 13px;
  color: var(--text-secondary);
  margin: 0;
}

.notification-time {
  font-size: 12px;
  color: var(--text-secondary);
  opacity: 0.7;
}

/* Card container */
.card-container {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(320px, 1fr));
  gap: 24px;
  padding: 32px 0;
}

/* Statistics Overview */
.stats-overview {
  padding: 32px 0;
}

.stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 24px;
}

.stat-card {
  background: var(--card-bg);
  border-radius: 16px;
  padding: 24px;
  box-shadow: var(--shadow-md);
  transition: var(--transition);
}

.stat-card:hover {
  transform: translateY(-4px);
  box-shadow: var(--shadow-lg);
}

.stat-header {
  display: flex;
  align-items: center;
  gap: 12px;
  margin-bottom: 16px;
}

.stat-header mat-icon {
  font-size: 24px;
  color: var(--accent-teal);
}

.stat-label {
  font-size: 13px;
  font-weight: 500;
  color: var(--text-secondary);
  text-transform: uppercase;
}

.stat-value {
  font-size: 36px;
  font-weight: 700;
  color: var(--primary-blue);
}

.stat-change {
  font-size: 13px;
  font-weight: 500;
  padding: 6px 12px;
  border-radius: 8px;
}

.stat-change.positive {
  background: rgba(34, 197, 94, 0.1);
  color: #22c55e;
}

.stat-change.neutral {
  background: rgba(37, 99, 235, 0.1);
  color: var(--secondary-blue);
}

/* Quick Actions */
.quick-actions {
  padding: 32px 0;
}

.section-title {
  font-size: 22px;
  font-weight: 600;
  color: var(--primary-blue);
  display: flex;
  align-items: center;
  gap: 12px;
  margin-bottom: 24px;
}

.section-title mat-icon {
  font-size: 24px;
  color: var(--accent-teal);
}

.actions-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(320px, 1fr));
  gap: 24px;
}

.action-card {
  background: var(--card-bg);
  border-radius: 16px;
  padding: 24px;
  display: flex;
  align-items: center;
  gap: 16px;
  box-shadow: var(--shadow-md);
  transition: var(--transition);
}

.action-card:hover {
  transform: translateY(-4px);
  box-shadow: var(--shadow-lg);
}

.action-icon {
  width: 56px;
  height: 56px;
  background: var(--accent-teal);
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.action-icon mat-icon {
  font-size: 28px;
  color: #ffffff;
}

.action-content h3 {
  font-size: 18px;
  font-weight: 600;
  color: var(--primary-blue);
  margin: 0 0 6px 0;
}

.action-content p {
  font-size: 14px;
  color: var(--text-secondary);
}

.action-arrow {
  font-size: 18px;
  color: var(--text-secondary);
  transition: var(--transition);
}

.action-card:hover .action-arrow {
  color: var(--accent-teal);
  transform: translateX(6px);
}

/* Recent Activity */
.recent-activity {
  padding: 32px 0;
}

.activity-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(420px, 1fr));
  gap: 24px;
}

.activity-section {
  background: var(--card-bg);
  border-radius: 16px;
  padding: 24px;
  box-shadow: var(--shadow-md);
  transition: var(--transition);
}

.activity-section:hover {
  transform: translateY(-4px);
  box-shadow: var(--shadow-lg);
}

.activity-title {
  font-size: 20px;
  font-weight: 600;
  color: var(--primary-blue);
  display: flex;
  align-items: center;
  gap: 12px;
  margin-bottom: 20px;
}

.activity-title mat-icon {
  font-size: 24px;
  color: var(--accent-teal);
}

.activity-list {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.activity-item {
  display: flex;
  align-items: center;
  gap: 16px;
  padding: 16px;
  background: var(--background-light);
  border-radius: 12px;
  transition: var(--transition);
}

.activity-item:hover {
  transform: translateX(6px);
  background: rgba(45, 212, 191, 0.05);
}

.activity-icon {
  width: 48px;
  height: 48px;
  background: rgba(45, 212, 191, 0.1);
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.activity-icon mat-icon {
  font-size: 24px;
  color: var(--accent-teal);
}

.activity-details h4 {
  font-size: 16px;
  font-weight: 600;
  color: var(--text-primary);
}

.activity-details p {
  font-size: 14px;
  color: var(--text-secondary);
}

.activity-date {
  font-size: 12px;
  color: var(--text-secondary);
  opacity: 0.7;
}

.activity-status {
  padding: 6px 12px;
  border-radius: 8px;
  font-size: 13px;
  font-weight: 500;
}

.activity-status.active {
  background: rgba(34, 197, 94, 0.1);
  color: #22c55e;
}

.activity-score {
  padding: 6px 12px;
  border-radius: 8px;
  font-size: 13px;
  font-weight: 600;
  background: rgba(37, 99, 235, 0.1);
  color: var(--secondary-blue);
}

/* Loading */
.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 48px;
  color: var(--text-secondary);
}

.loading-spinner mat-icon {
  font-size: 36px;
  color: var(--accent-teal);
  animation: spin 1.5s linear infinite;
}

/* Responsive */
@media (max-width: 1200px) {
  .dashboard {
    grid-template-columns: 260px 1fr;
  }

  .search-container {
    width: 300px;
  }
}

@media (max-width: 992px) {
  .dashboard {
    grid-template-columns: 1fr;
    grid-template-areas: "main";
  }

  app-sidebar-recruiter {
    display: none;
  }

  .dashboard-main {
    margin: 16px;
    padding: 24px;
  }

  .header-content {
    flex-direction: column;
    gap: 20px;
    align-items: flex-start;
  }

  .search-container {
    width: 100%;
  }
}

@media (max-width: 768px) {
  .stats-grid,
  .actions-grid,
  .activity-grid {
    grid-template-columns: 1fr;
  }

  .dashboard-main {
    margin: 8px;
    padding: 16px;
    border-radius: 12px;
  }

  .dashboard-header {
    padding: 16px;
  }

  .stats-overview,
  .quick-actions,
  .recent-activity {
    padding: 16px 0;
  }
}
