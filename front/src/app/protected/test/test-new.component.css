/* ========================================================================== */
/* 🌟 Ultra Professional Test Page - Modern Design System */
/* ========================================================================== */
@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&family=JetBrains+Mono:wght@400;500;600&display=swap');

:root {
  /* Primary Brand Colors */
  --primary-blue: #001040FF;
  --primary-blue-light: #001660FF;
  --primary-blue-dark: #000830;

  /* Accent Colors */
  --accent-orange: #FF6B35;
  --accent-orange-light: #FF8A65;
  --accent-gold: #FFD700;

  /* Neutral Palette */
  --white: #FFFFFF;
  --gray-50: #F8FAFC;
  --gray-100: #F1F5F9;
  --gray-200: #E2E8F0;
  --gray-300: #CBD5E1;
  --gray-400: #94A3B8;
  --gray-500: #64748B;
  --gray-600: #475569;
  --gray-700: #334155;
  --gray-800: #1E293B;
  --gray-900: #0F172A;

  /* Status Colors */
  --success: #10B981;
  --success-light: #34D399;
  --warning: #F59E0B;
  --error: #EF4444;

  /* Shadows */
  --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
  --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
  --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
  --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
  --shadow-2xl: 0 25px 50px -12px rgba(0, 0, 0, 0.25);

  /* Border Radius */
  --radius-sm: 0.375rem;
  --radius-md: 0.5rem;
  --radius-lg: 0.75rem;
  --radius-xl: 1rem;
  --radius-2xl: 1.5rem;

  /* Transitions */
  --transition-fast: 0.15s cubic-bezier(0.4, 0, 0.2, 1);
  --transition-normal: 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  --transition-slow: 0.5s cubic-bezier(0.4, 0, 0.2, 1);
}

/* ========================================================================== */
/* 🎯 Global Styles */
/* ========================================================================== */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  font-feature-settings: 'cv02', 'cv03', 'cv04', 'cv11';
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

/* ========================================================================== */
/* 🌟 Main Container - Ultra Modern */
/* ========================================================================== */
.quiz-container {
  min-height: 100vh;
  background: linear-gradient(135deg, var(--primary-blue) 0%, var(--primary-blue-light) 50%, var(--primary-blue-dark) 100%);
  background-size: 400% 400%;
  animation: gradientShift 15s ease infinite;
  padding: 2rem;
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
  overflow: hidden;
}

.quiz-container::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background:
    radial-gradient(circle at 20% 80%, rgba(255, 107, 53, 0.1) 0%, transparent 50%),
    radial-gradient(circle at 80% 20%, rgba(255, 215, 0, 0.1) 0%, transparent 50%),
    radial-gradient(circle at 40% 40%, rgba(255, 138, 101, 0.05) 0%, transparent 50%);
  animation: floatingOrbs 20s ease-in-out infinite;
}

@keyframes gradientShift {
  0%, 100% { background-position: 0% 50%; }
  50% { background-position: 100% 50%; }
}

@keyframes floatingOrbs {
  0%, 100% { transform: translate(0, 0) rotate(0deg); }
  33% { transform: translate(30px, -30px) rotate(120deg); }
  66% { transform: translate(-20px, 20px) rotate(240deg); }
}

/* ========================================================================== */
/* 🎨 Main Card - Glass Morphism Design */
/* ========================================================================== */
.quiz-body-card {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(20px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: var(--radius-2xl);
  box-shadow: var(--shadow-2xl);
  padding: 3rem;
  max-width: 900px;
  width: 100%;
  position: relative;
  z-index: 10;
  animation: slideInUp 0.8s cubic-bezier(0.16, 1, 0.3, 1);
  transition: transform var(--transition-normal), box-shadow var(--transition-normal);
}

.quiz-body-card:hover {
  transform: translateY(-8px);
  box-shadow: 0 32px 64px -12px rgba(0, 16, 64, 0.4);
}

@keyframes slideInUp {
  from {
    opacity: 0;
    transform: translateY(60px) scale(0.95);
  }
  to {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

/* ========================================================================== */
/* 🕒 Top Bar - Premium Header */
/* ========================================================================== */
.quiz-top-bar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1.5rem 2rem;
  margin: -3rem -3rem 2rem -3rem;
  background: linear-gradient(135deg, var(--white) 0%, var(--gray-50) 100%);
  border-bottom: 1px solid var(--gray-200);
  border-radius: var(--radius-2xl) var(--radius-2xl) 0 0;
  backdrop-filter: blur(10px);
}

.quiz-top-bar .date {
  font-size: 1.125rem;
  font-weight: 600;
  color: var(--gray-700);
  letter-spacing: -0.025em;
  font-family: 'JetBrains Mono', monospace;
}

.timer-panel {
  background: linear-gradient(135deg, var(--primary-blue) 0%, var(--primary-blue-light) 100%);
  color: var(--white);
  padding: 1rem 1.5rem;
  border-radius: var(--radius-lg);
  text-align: center;
  min-width: 140px;
  box-shadow: var(--shadow-lg);
  position: relative;
  overflow: hidden;
  transition: all var(--transition-normal);
}

.timer-panel::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transition: left 0.5s;
}

.timer-panel:hover::before {
  left: 100%;
}

.timer-title {
  font-size: 0.75rem;
  text-transform: uppercase;
  letter-spacing: 0.1em;
  margin-bottom: 0.25rem;
  opacity: 0.9;
  font-weight: 500;
}

.timer {
  font-size: 1.5rem;
  font-weight: 700;
  font-family: 'JetBrains Mono', monospace;
}

/* ========================================================================== */
/* 🎯 Quiz Title - Elegant Typography */
/* ========================================================================== */
.quiz-title {
  font-size: 2.5rem;
  font-weight: 800;
  color: var(--gray-900);
  margin-bottom: 2rem;
  text-align: center;
  letter-spacing: -0.05em;
  line-height: 1.2;
  position: relative;
  animation: fadeInScale 1s cubic-bezier(0.16, 1, 0.3, 1);
}

.quiz-title::after {
  content: '';
  position: absolute;
  bottom: -0.5rem;
  left: 50%;
  transform: translateX(-50%);
  width: 80px;
  height: 4px;
  background: linear-gradient(90deg, var(--accent-orange) 0%, var(--accent-gold) 100%);
  border-radius: 2px;
  animation: expandLine 1s ease-out 0.5s both;
}

@keyframes fadeInScale {
  from {
    opacity: 0;
    transform: scale(0.9);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}

@keyframes expandLine {
  from { width: 0; }
  to { width: 80px; }
}

.centered-title {
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 1rem;
}

.flag-icon {
  font-size: 2.5rem;
  color: var(--accent-orange);
  filter: drop-shadow(0 4px 8px rgba(255, 107, 53, 0.3));
  animation: bounce 2s ease-in-out infinite;
}

@keyframes bounce {
  0%, 20%, 50%, 80%, 100% { transform: translateY(0); }
  40% { transform: translateY(-10px); }
  60% { transform: translateY(-5px); }
}

/* ========================================================================== */
/* 💬 Question Section - Modern Layout */
/* ========================================================================== */
.question-title {
  font-size: 1.875rem;
  font-weight: 700;
  color: var(--gray-900);
  margin-bottom: 1.5rem;
  letter-spacing: -0.025em;
  line-height: 1.4;
  text-align: center;
  animation: slideInLeft 0.6s cubic-bezier(0.16, 1, 0.3, 1);
}

.question-text {
  font-size: 1.125rem;
  color: var(--gray-600);
  margin-bottom: 2rem;
  line-height: 1.7;
  text-align: justify;
  animation: slideInRight 0.6s cubic-bezier(0.16, 1, 0.3, 1);
}

@keyframes slideInLeft {
  from {
    opacity: 0;
    transform: translateX(-30px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes slideInRight {
  from {
    opacity: 0;
    transform: translateX(30px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

/* ========================================================================== */
/* ✅ Options Section - Premium Interactive Design */
/* ========================================================================== */
.options {
  display: flex;
  flex-direction: column;
  gap: 1rem;
  background: linear-gradient(135deg, var(--gray-50) 0%, var(--white) 100%);
  padding: 2rem;
  border-radius: var(--radius-xl);
  border: 1px solid var(--gray-200);
  box-shadow: var(--shadow-md);
  position: relative;
  overflow: hidden;
  animation: fadeInUp 0.8s cubic-bezier(0.16, 1, 0.3, 1);
}

.options::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: linear-gradient(90deg, var(--accent-orange) 0%, var(--accent-gold) 100%);
  border-radius: var(--radius-xl) var(--radius-xl) 0 0;
}

.options mat-radio-group {
  display: flex;
  flex-direction: column;
  gap: inherit;
}

/* Individual Option - Ultra Modern Card */
.option {
  background: var(--white);
  border-radius: var(--radius-lg);
  padding: 1.5rem;
  display: flex;
  align-items: center;
  gap: 1rem;
  border: 2px solid var(--gray-200);
  cursor: pointer;
  transition: all var(--transition-normal);
  box-shadow: var(--shadow-sm);
  position: relative;
  overflow: hidden;
  animation: slideInUp 0.5s cubic-bezier(0.16, 1, 0.3, 1);
}

.option::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 107, 53, 0.05), transparent);
  transition: left 0.6s ease;
}

.option:hover::before {
  left: 100%;
}

.option:hover {
  border-color: var(--accent-orange);
  box-shadow: 0 8px 25px -5px rgba(255, 107, 53, 0.25);
  transform: translateY(-4px);
  background: linear-gradient(135deg, var(--white) 0%, rgba(255, 107, 53, 0.02) 100%);
}

.option:active {
  transform: translateY(-2px);
}

.option.selected {
  border-color: var(--primary-blue);
  background: linear-gradient(135deg, var(--white) 0%, rgba(0, 16, 64, 0.05) 100%);
  box-shadow: 0 8px 25px -5px rgba(0, 16, 64, 0.25);
}

/* Radio Button Styling */
.option mat-radio-button {
  margin: 0;
  flex-shrink: 0;
}

.option mat-radio-button ::ng-deep .mat-radio-outer-circle {
  border-color: var(--gray-400);
  border-width: 2px;
  transition: all var(--transition-normal);
}

.option mat-radio-button ::ng-deep .mat-radio-inner-circle {
  background-color: var(--primary-blue);
  transform: scale(0);
  transition: all var(--transition-normal);
}

.option mat-radio-button ::ng-deep .mat-radio-checked .mat-radio-outer-circle {
  border-color: var(--primary-blue);
}

.option mat-radio-button ::ng-deep .mat-radio-checked .mat-radio-inner-circle {
  transform: scale(0.5);
}

/* Option Text */
.option-text {
  font-weight: 500;
  color: var(--gray-700);
  font-size: 1rem;
  line-height: 1.6;
  flex: 1;
  transition: color var(--transition-normal);
}

.option:hover .option-text {
  color: var(--gray-900);
}

.option.selected .option-text {
  color: var(--primary-blue);
  font-weight: 600;
}

@keyframes slideInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* ========================================================================== */
/* 🎯 Progress Section - Modern Indicators */
/* ========================================================================== */
.progress-section {
  margin: 2rem 0;
  padding: 1.5rem;
  background: linear-gradient(135deg, var(--gray-50) 0%, var(--white) 100%);
  border-radius: var(--radius-xl);
  border: 1px solid var(--gray-200);
  box-shadow: var(--shadow-sm);
  animation: fadeInScale 0.6s cubic-bezier(0.16, 1, 0.3, 1);
}

.progress-info {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1rem;
  flex-wrap: wrap;
  gap: 0.5rem;
}

.progress-text {
  font-size: 0.875rem;
  font-weight: 600;
  color: var(--gray-600);
  font-family: 'JetBrains Mono', monospace;
}

.completion-status {
  font-size: 0.875rem;
  font-weight: 600;
  padding: 0.5rem 1rem;
  border-radius: var(--radius-lg);
  display: flex;
  align-items: center;
  gap: 0.5rem;
  animation: slideInRight 0.5s cubic-bezier(0.16, 1, 0.3, 1);
}

.completion-status.completed {
  background: linear-gradient(135deg, var(--success-light) 0%, var(--success) 100%);
  color: var(--white);
  box-shadow: 0 4px 12px rgba(16, 185, 129, 0.3);
}

.completion-status.submitting {
  background: linear-gradient(135deg, var(--warning) 0%, var(--accent-orange) 100%);
  color: var(--white);
  box-shadow: 0 4px 12px rgba(245, 158, 11, 0.3);
  animation: pulse 1.5s ease-in-out infinite;
}

@keyframes pulse {
  0%, 100% {
    opacity: 1;
    transform: scale(1);
  }
  50% {
    opacity: 0.8;
    transform: scale(1.05);
  }
}

/* Progress Bar */
.progress-bar {
  height: 8px;
  background: var(--gray-200);
  border-radius: var(--radius-sm);
  overflow: hidden;
  position: relative;
}

.progress-bar ::ng-deep .mat-mdc-progress-bar-fill::after {
  background: linear-gradient(90deg, var(--primary-blue) 0%, var(--accent-orange) 100%);
  border-radius: var(--radius-sm);
  box-shadow: 0 0 10px rgba(0, 16, 64, 0.3);
}

/* ========================================================================== */
/* 🎯 Stepper - Ultra Modern Design */
/* ========================================================================== */
::ng-deep .mat-horizontal-stepper-header-container {
  background: linear-gradient(135deg, var(--gray-50) 0%, var(--white) 100%);
  border-radius: var(--radius-xl);
  padding: 1rem;
  margin-bottom: 2rem;
  box-shadow: var(--shadow-sm);
  border: 1px solid var(--gray-200);
}

::ng-deep .mat-step-header {
  padding: 0.75rem 1rem !important;
  border-radius: var(--radius-lg);
  transition: all var(--transition-normal);
  position: relative;
  overflow: hidden;
}

::ng-deep .mat-step-header:hover {
  background: rgba(0, 16, 64, 0.05);
}

::ng-deep .mat-step-header.cdk-keyboard-focused,
::ng-deep .mat-step-header.cdk-program-focused {
  background: rgba(0, 16, 64, 0.1);
}

::ng-deep .mat-step-icon {
  background: var(--gray-300) !important;
  color: var(--white) !important;
  border-radius: 50%;
  width: 32px !important;
  height: 32px !important;
  transition: all var(--transition-normal);
  box-shadow: var(--shadow-sm);
}

::ng-deep .mat-step-icon-selected {
  background: var(--primary-blue) !important;
  box-shadow: 0 4px 12px rgba(0, 16, 64, 0.3);
}

::ng-deep .mat-step-icon-state-done {
  background: var(--success) !important;
  box-shadow: 0 4px 12px rgba(16, 185, 129, 0.3);
}

::ng-deep .mat-step-label {
  font-weight: 600;
  color: var(--gray-600);
  font-size: 0.875rem;
  margin-top: 0.5rem;
}

::ng-deep .mat-step-label-selected {
  color: var(--primary-blue);
}

/* ========================================================================== */
/* ⬅️➡️ Navigation Footer - Premium Buttons */
/* ========================================================================== */
.quiz-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: 3rem;
  gap: 1rem;
  flex-wrap: wrap;
}

.quiz-footer button {
  padding: 1rem 2rem;
  font-size: 1rem;
  font-weight: 600;
  border-radius: var(--radius-lg);
  border: none;
  cursor: pointer;
  transition: all var(--transition-normal);
  position: relative;
  overflow: hidden;
  min-width: 140px;
  font-family: 'Inter', sans-serif;
  letter-spacing: -0.025em;
}

.quiz-footer button:not(:disabled) {
  background: linear-gradient(135deg, var(--primary-blue) 0%, var(--primary-blue-light) 100%);
  color: var(--white);
  box-shadow: var(--shadow-lg);
}

.quiz-footer button:not(:disabled)::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transition: left 0.6s ease;
}

.quiz-footer button:not(:disabled):hover::before {
  left: 100%;
}

.quiz-footer button:not(:disabled):hover {
  transform: translateY(-3px);
  box-shadow: 0 12px 24px rgba(0, 16, 64, 0.4);
  background: linear-gradient(135deg, var(--primary-blue-light) 0%, var(--accent-orange) 100%);
}

.quiz-footer button:not(:disabled):active {
  transform: translateY(-1px);
}

.quiz-footer button:disabled {
  background: var(--gray-300);
  color: var(--gray-500);
  cursor: not-allowed;
  box-shadow: none;
}

/* Auto Submit Info */
.auto-submit-info {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  padding: 1rem 1.5rem;
  background: linear-gradient(135deg, rgba(59, 130, 246, 0.1) 0%, rgba(147, 197, 253, 0.1) 100%);
  border: 2px solid rgba(59, 130, 246, 0.2);
  border-radius: var(--radius-lg);
  color: var(--primary-blue);
  font-size: 0.875rem;
  font-weight: 500;
  box-shadow: var(--shadow-sm);
  animation: slideInUp 0.5s cubic-bezier(0.16, 1, 0.3, 1);
}

.auto-submit-info mat-icon {
  font-size: 1.25rem;
  width: 1.25rem;
  height: 1.25rem;
  color: var(--primary-blue);
}

/* Submitting Indicator */
.submitting-indicator {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  padding: 1rem 1.5rem;
  background: linear-gradient(135deg, rgba(245, 158, 11, 0.1) 0%, rgba(251, 191, 36, 0.1) 100%);
  border: 2px solid rgba(245, 158, 11, 0.3);
  border-radius: var(--radius-lg);
  color: var(--warning);
  font-size: 0.875rem;
  font-weight: 600;
  box-shadow: var(--shadow-sm);
  animation: pulse 1.5s ease-in-out infinite;
}

.submitting-indicator mat-progress-spinner {
  --mdc-circular-progress-active-indicator-color: var(--warning);
}

/* ========================================================================== */
/* 📱 Responsive Design - Mobile First */
/* ========================================================================== */
@media (max-width: 768px) {
  .quiz-container {
    padding: 1rem;
  }

  .quiz-body-card {
    padding: 1.5rem;
    margin: 0;
  }

  .quiz-top-bar {
    margin: -1.5rem -1.5rem 1.5rem -1.5rem;
    padding: 1rem;
    flex-direction: column;
    gap: 1rem;
  }

  .quiz-title {
    font-size: 2rem;
    margin-bottom: 1.5rem;
  }

  .question-title {
    font-size: 1.5rem;
    margin-bottom: 1rem;
  }

  .question-text {
    font-size: 1rem;
    margin-bottom: 1.5rem;
  }

  .options {
    padding: 1.5rem;
    gap: 0.75rem;
  }

  .option {
    padding: 1rem;
    gap: 0.75rem;
  }

  .option-text {
    font-size: 0.875rem;
  }

  .progress-section {
    padding: 1rem;
    margin: 1.5rem 0;
  }

  .progress-info {
    flex-direction: column;
    align-items: flex-start;
    gap: 0.75rem;
  }

  .quiz-footer {
    margin-top: 2rem;
    flex-direction: column;
    gap: 0.75rem;
  }

  .quiz-footer button {
    width: 100%;
    padding: 0.875rem 1.5rem;
  }

  .auto-submit-info,
  .submitting-indicator {
    padding: 0.875rem 1rem;
    font-size: 0.8rem;
  }
}

@media (max-width: 480px) {
  .quiz-body-card {
    padding: 1rem;
  }

  .quiz-title {
    font-size: 1.75rem;
  }

  .question-title {
    font-size: 1.25rem;
  }

  .timer-panel {
    min-width: 120px;
    padding: 0.75rem 1rem;
  }

  .timer {
    font-size: 1.25rem;
  }

  .flag-icon {
    font-size: 2rem;
  }
}

/* ========================================================================== */
/* 🎨 Additional Animations & Effects */
/* ========================================================================== */
@keyframes fadeInScale {
  from {
    opacity: 0;
    transform: scale(0.95);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}

/* Smooth scroll behavior */
html {
  scroll-behavior: smooth;
}

/* Focus styles for accessibility */
*:focus {
  outline: 2px solid var(--primary-blue);
  outline-offset: 2px;
}

/* Selection styles */
::selection {
  background: rgba(0, 16, 64, 0.2);
  color: var(--primary-blue);
}
