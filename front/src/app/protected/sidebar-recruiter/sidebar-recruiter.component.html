<div class="sidebar" [ngClass]="{ collapsed: collapsed }">
  <!-- Toggle Button -->
  <button class="toggle-btn" (click)="toggleSidebar()">
    <mat-icon>{{ collapsed ? 'chevron_right' : 'chevron_left' }}</mat-icon>
  </button>

  <!-- User Profile Section -->
  <div class="user-profile" (click)="navigateToProfile()">
    <div class="profile-avatar">
      <img *ngIf="avatarUrl && !isLoading" [src]="avatarUrl" class="avatar" alt="User Avatar" />
      <div *ngIf="!avatarUrl && !isLoading" class="avatar-placeholder">
        <mat-icon class="avatar-icon">person</mat-icon>
      </div>
      <div *ngIf="isLoading" class="avatar-skeleton"></div>
      <div class="status-indicator"></div>
    </div>
    <div class="profile-info" *ngIf="!collapsed">
      <span class="user-name" [class.skeleton]="isLoading">{{ isLoading ? '' : userName }}</span>
      <span class="user-email" [class.skeleton]="isLoading">{{ isLoading ? '' : userEmail }}</span>
    </div>
  </div>

  <!-- Sidebar Header -->
  <div class="sidebar-header" *ngIf="!collapsed">
    <div class="logo">
      <div class="logo-container">
        <mat-icon class="logo-icon">dashboard</mat-icon>
        <div class="logo-glow"></div>
      </div>
      <h2 class="dashboard-title">Dashboard Recruteur</h2>
      <p class="dashboard-subtitle">Gestion professionnelle</p>
    </div>
  </div>

  <!-- Navigation Menu -->
  <nav class="sidebar-nav">
    <ul class="sidebar-menu">
      <li *ngFor="let item of menuItems"
          [class.active]="item.active"
          routerLinkActive="active">
        <a [routerLink]="item.route" class="menu-link">
          <div class="menu-icon-container">
            <mat-icon class="menu-icon">{{ item.icon }}</mat-icon>
            <div class="icon-glow"></div>
          </div>
          <span class="menu-label" *ngIf="!collapsed">{{ item.label }}</span>
          <div class="menu-indicator" *ngIf="item.active"></div>
        </a>
      </li>
    </ul>
  </nav>

  <!-- Sidebar Footer -->
  <div class="sidebar-footer">
    <button class="logout-btn" (click)="logout()">
      <div class="logout-icon-container">
        <mat-icon class="logout-icon">logout</mat-icon>
        <div class="logout-glow"></div>
      </div>
      <span *ngIf="!collapsed">Déconnexion</span>
    </button>
  </div>
</div>
