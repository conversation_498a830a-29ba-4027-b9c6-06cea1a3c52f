import { Component, OnInit } from '@angular/core';
import {MatIcon} from "@angular/material/icon";
import {RouterLink, RouterLinkActive, Router} from "@angular/router";
import {Ng<PERSON>lass, NgForOf, NgIf} from '@angular/common';
import { UserService } from '../../services/user/user.service';
import { OidcSecurityService } from 'angular-auth-oidc-client';

@Component({
  selector: 'app-sidebar-recruiter',
  imports: [
    MatIcon,
    RouterLink,
    RouterLinkActive,
    NgClass,
    NgIf,
    NgForOf
  ],
  templateUrl: './sidebar-recruiter.component.html',
  standalone: true,
  styleUrl: './sidebar-recruiter.component.css'
})
export class SidebarRecruiterComponent implements OnInit {
  avatarUrl: string | null = null;
  userName: string = 'Recruteur';
  userEmail: string = '';
  collapsed: boolean = false;
  sidebarOpen: boolean = true;
  currentRoute: string = '';
  isLoading: boolean = true;

  // Menu items with dynamic data
  menuItems = [
    {
      route: '/dashboard',
      icon: 'dashboard',
      label: 'Dashboard',
      active: false
    },
    {
      route: '/dashboard/workspace',
      icon: 'business',
      label: 'Voir Workspace',
      active: false
    },
    {
      route: '/dashboard/posts',
      icon: 'article',
      label: 'Vos Posts',
      active: false
    },
    {
      route: '/dashboard/settings',
      icon: 'settings',
      label: 'Settings',
      active: false
    },
    {
      route: '/dashboard/candidats',
      icon: 'people',
      label: 'Voir Candidats',
      active: false
    },
    {
      route: '/dashboard/test-results',
      icon: 'assessment',
      label: 'Résultats de Test',
      active: false
    }
  ];

  constructor(
    private userService: UserService,
    private oidcSecurityService: OidcSecurityService,
    private router: Router
  ) {}

  ngOnInit() {
    this.loadUserProfile();
    this.updateActiveRoute();

    // Écouter les changements de route
    this.router.events.subscribe(() => {
      this.updateActiveRoute();
    });
  }

  loadUserProfile() {
    this.isLoading = true;
    this.userService.getUserProfile().subscribe({
      next: (user) => {
        this.userName = user.firstName && user.lastName
          ? `${user.firstName} ${user.lastName}`
          : user.email || 'Recruteur';
        this.userEmail = user.email || '';
        this.avatarUrl = user.profilePicture || null;
        this.isLoading = false;
      },
      error: (error) => {
        console.error('Erreur lors du chargement du profil:', error);
        this.userName = 'Recruteur';
        this.userEmail = '<EMAIL>';
        this.isLoading = false;
      }
    });
  }

  updateActiveRoute() {
    this.currentRoute = this.router.url;
    this.menuItems.forEach(item => {
      if (item.route === '/dashboard' && this.currentRoute === '/dashboard') {
        item.active = true;
      } else if (item.route !== '/dashboard') {
        item.active = this.currentRoute.includes(item.route);
      } else {
        item.active = false;
      }
    });
  }

  logout() {
    this.oidcSecurityService.logoff().subscribe({
      next: () => {
        this.router.navigate(['/auth/signin']);
      },
      error: (error) => {
        console.error('Erreur lors de la déconnexion:', error);
        this.router.navigate(['/auth/signin']);
      }
    });
  }

  toggleSidebar() {
    this.collapsed = !this.collapsed;
  }

  navigateToProfile() {
    this.router.navigate(['/dashboard/profil']);
  }
}

