/* Simple & Elegant Recruiter Sidebar */
@import url('https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&display=swap');

:root {
  --navy-blue: #1e3a8a;
  --dark-navy: #1e40af;
  --orange: #f97316;
  --light-orange: #fb923c;
  --white: #ffffff;
  --light-gray: #f8fafc;
  --text-dark: #1f2937;
  --text-light: #6b7280;
  --border-light: #e5e7eb;
  --shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
}

.sidebar {
  width: 260px;
  height: 100vh;
  position: fixed;
  top: 0;
  left: 0;
  background: var(--white);
  border-right: 1px solid var(--border-light);
  font-family: 'Inter', sans-serif;
  transition: all 0.3s ease;
  z-index: 1000;
  box-shadow: var(--shadow);
}

.sidebar.collapsed {
  width: 70px;
}

/* Toggle Button */
.toggle-btn {
  position: absolute;
  top: 20px;
  right: -15px;
  width: 30px;
  height: 30px;
  background: var(--orange);
  color: var(--white);
  border: none;
  border-radius: 50%;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: var(--shadow);
}

.toggle-btn:hover {
  background: var(--light-orange);
  transform: scale(1.1);
}

.toggle-btn mat-icon {
  font-size: 18px;
}

/* User Profile */
.user-profile {
  display: flex;
  align-items: center;
  padding: 20px;
  border-bottom: 1px solid var(--border-light);
  cursor: pointer;
  transition: all 0.3s ease;
}

.user-profile:hover {
  background: var(--light-gray);
}

.profile-avatar {
  position: relative;
  margin-right: 12px;
}

.avatar,
.avatar-placeholder {
  width: 40px;
  height: 40px;
  border-radius: 50%;
}

.avatar {
  object-fit: cover;
  border: 2px solid var(--orange);
}

.avatar-placeholder {
  background: var(--navy-blue);
  display: flex;
  align-items: center;
  justify-content: center;
}

.avatar-icon {
  font-size: 20px;
  color: var(--white);
}

.avatar-skeleton {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  background: var(--border-light);
  animation: pulse 1.5s infinite;
}

.status-indicator {
  position: absolute;
  bottom: 0;
  right: 0;
  width: 10px;
  height: 10px;
  background: #22c55e;
  border: 2px solid var(--white);
  border-radius: 50%;
}

.profile-info {
  flex: 1;
  transition: all 0.3s ease;
}

.user-name {
  font-size: 14px;
  font-weight: 600;
  color: var(--text-dark);
  margin-bottom: 2px;
}

.user-email {
  font-size: 12px;
  color: var(--text-light);
}

.skeleton {
  background: var(--border-light);
  border-radius: 4px;
  height: 14px;
  width: 100px;
  animation: pulse 1.5s infinite;
}

/* Sidebar Header */
.sidebar-header {
  padding: 20px;
  text-align: center;
  border-bottom: 1px solid var(--border-light);
}

.logo {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 8px;
}

.logo-container {
  position: relative;
}

.logo-icon {
  font-size: 32px;
  color: var(--orange);
}

.dashboard-title {
  font-size: 18px;
  font-weight: 700;
  color: var(--navy-blue);
  margin: 0;
}

.dashboard-subtitle {
  font-size: 11px;
  color: var(--text-light);
  margin: 0;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

/* Navigation Menu */
.sidebar-nav {
  flex: 1;
  padding: 20px 0;
}

.sidebar-menu {
  list-style: none;
  padding: 0;
  margin: 0;
}

.sidebar-menu li {
  margin: 4px 0;
}

.menu-link {
  display: flex;
  align-items: center;
  padding: 12px 20px;
  color: var(--text-light);
  text-decoration: none;
  font-size: 14px;
  font-weight: 500;
  transition: all 0.3s ease;
  position: relative;
}

.menu-link:hover {
  background: var(--light-gray);
  color: var(--navy-blue);
}

.sidebar-menu li.active .menu-link {
  background: linear-gradient(90deg, var(--orange), var(--light-orange));
  color: var(--white);
  font-weight: 600;
}

.menu-icon-container {
  margin-right: 12px;
  width: 20px;
  display: flex;
  justify-content: center;
}

.menu-icon {
  font-size: 20px;
  transition: all 0.3s ease;
}

.menu-label {
  flex: 1;
}

/* Sidebar Footer */
.sidebar-footer {
  padding: 20px;
  border-top: 1px solid var(--border-light);
}

.logout-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;
  padding: 12px;
  background: var(--navy-blue);
  color: var(--white);
  border: none;
  border-radius: 8px;
  font-size: 14px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
}

.logout-btn:hover {
  background: var(--dark-navy);
  transform: translateY(-1px);
}

.logout-icon-container {
  margin-right: 8px;
}

.logout-icon {
  font-size: 18px;
}

/* Animations */
@keyframes pulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
}

/* Responsive */
@media (max-width: 768px) {
  .sidebar {
    width: 70px;
  }
  
  .profile-info,
  .sidebar-header,
  .menu-label,
  .logout-btn span {
    display: none;
  }
  
  .user-profile,
  .menu-link,
  .logout-btn {
    justify-content: center;
  }
  
  .menu-icon-container {
    margin: 0;
  }
  
  .logout-icon-container {
    margin: 0;
  }
}

.sidebar.collapsed .profile-info,
.sidebar.collapsed .sidebar-header,
.sidebar.collapsed .menu-label,
.sidebar.collapsed .logout-btn span {
  display: none;
}

.sidebar.collapsed .user-profile,
.sidebar.collapsed .menu-link,
.sidebar.collapsed .logout-btn {
  justify-content: center;
}

.sidebar.collapsed .menu-icon-container {
  margin: 0;
}

.sidebar.collapsed .logout-icon-container {
  margin: 0;
}
