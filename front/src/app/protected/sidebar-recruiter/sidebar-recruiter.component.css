/* Premium Recruiter Sidebar - Ultra Modern */
@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&display=swap');

:root {
  --primary-gradient: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  --secondary-gradient: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
  --accent-gradient: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
  --success-gradient: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);
  --warning-gradient: linear-gradient(135deg, #fa709a 0%, #fee140 100%);

  --sidebar-bg: linear-gradient(180deg, #1a1a2e 0%, #16213e 50%, #0f3460 100%);
  --sidebar-overlay: rgba(255, 255, 255, 0.05);
  --sidebar-border: rgba(255, 255, 255, 0.1);

  --text-primary: #ffffff;
  --text-secondary: rgba(255, 255, 255, 0.8);
  --text-muted: rgba(255, 255, 255, 0.6);
  --text-dim: rgba(255, 255, 255, 0.4);

  --glow-blue: rgba(79, 172, 254, 0.3);
  --glow-purple: rgba(102, 126, 234, 0.3);
  --glow-pink: rgba(245, 87, 108, 0.3);

  --shadow-elegant: 0 20px 60px rgba(0, 0, 0, 0.4);
  --shadow-glow: 0 0 30px rgba(79, 172, 254, 0.2);
}

.sidebar {
  width: 240px;
  height: 100vh;
  position: fixed;
  top: 0;
  left: 0;
  background: var(--sidebar-dark);
  font-family: 'Inter', sans-serif;
  transition: all 0.3s ease;
  z-index: 1000;
  box-shadow: var(--shadow-dark);
  overflow: hidden;
  display: flex;
  flex-direction: column;
}

.sidebar.collapsed {
  width: 70px;
}

/* Toggle Button - Hidden for ADNIA style */
.toggle-btn {
  display: none;
}

/* Header Brand - ADNIA Style */
.user-profile {
  display: none;
}

.profile-avatar {
  position: relative;
  margin-right: 12px;
}

.avatar,
.avatar-placeholder {
  width: 40px;
  height: 40px;
  border-radius: 50%;
}

.avatar {
  object-fit: cover;
  border: 3px solid var(--accent-gold);
  box-shadow: 0 8px 25px rgba(245, 158, 11, 0.2);
}

.avatar-placeholder {
  background: var(--gradient-executive);
  display: flex;
  align-items: center;
  justify-content: center;
  border: 3px solid var(--accent-gold);
}

.avatar-icon {
  font-size: 24px;
  color: var(--white);
}

.avatar-skeleton {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  background: var(--border-light);
  animation: pulse 1.5s infinite;
}

.status-indicator {
  position: absolute;
  bottom: 0;
  right: 0;
  width: 10px;
  height: 10px;
  background: #22c55e;
  border: 2px solid var(--white);
  border-radius: 50%;
}

.profile-info {
  flex: 1;
  transition: all 0.3s ease;
}

.user-name {
  font-size: 16px;
  font-weight: 600;
  color: var(--text-primary);
  margin-bottom: 4px;
  letter-spacing: -0.2px;
}

.user-email {
  font-size: 13px;
  color: var(--text-muted);
  font-weight: 400;
}

.skeleton {
  background: var(--border-light);
  border-radius: 4px;
  height: 14px;
  width: 100px;
  animation: pulse 1.5s infinite;
}

/* Sidebar Header - ADNIA Brand */
.sidebar-header {
  padding: 30px 20px;
  border-bottom: 1px solid var(--border-subtle);
}

.logo {
  display: flex;
  align-items: center;
  gap: 12px;
}

.logo-container {
  display: flex;
  align-items: center;
}

.logo-icon {
  font-size: 24px;
  color: var(--white);
  margin-right: 8px;
}

.dashboard-title {
  font-size: 20px;
  font-weight: 700;
  color: var(--white);
  margin: 0;
  letter-spacing: 1px;
}

.dashboard-subtitle {
  display: none;
}

/* Navigation Menu - ADNIA Style */
.sidebar-nav {
  flex: 1;
  padding: 20px 0;
}

.sidebar-menu {
  list-style: none;
  padding: 0;
  margin: 0;
}

.sidebar-menu li {
  margin: 0;
}

.menu-link {
  display: flex;
  align-items: center;
  padding: 15px 20px;
  color: var(--text-muted);
  text-decoration: none;
  font-size: 14px;
  font-weight: 500;
  transition: all 0.3s ease;
  position: relative;
  border-left: 3px solid transparent;
}

.menu-link:hover {
  background: var(--sidebar-darker);
  color: var(--text-light);
  border-left-color: var(--accent-teal);
}

.sidebar-menu li.active .menu-link {
  background: var(--sidebar-darker);
  color: var(--white);
  border-left-color: var(--accent-teal);
  font-weight: 600;
}

.menu-icon-container {
  margin-right: 15px;
  width: 18px;
  display: flex;
  justify-content: center;
}

.menu-icon {
  font-size: 18px;
  transition: all 0.3s ease;
}

.sidebar-menu li.active .menu-icon {
  color: var(--accent-teal);
}

.menu-label {
  flex: 1;
}

/* Sidebar Footer - ADNIA Style */
.sidebar-footer {
  padding: 20px;
  border-top: 1px solid var(--border-subtle);
  display: flex;
  flex-direction: column;
  gap: 5px;
}

.settings-btn,
.logout-btn {
  display: flex;
  align-items: center;
  justify-content: flex-start;
  width: 100%;
  padding: 15px 20px;
  background: transparent;
  border: none;
  color: var(--text-muted);
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
  border-left: 3px solid transparent;
}

.settings-btn:hover,
.logout-btn:hover {
  background: var(--sidebar-darker);
  color: var(--text-light);
  border-left-color: var(--accent-teal);
}

.settings-icon-container,
.logout-icon-container {
  margin-right: 15px;
  width: 18px;
  display: flex;
  justify-content: center;
}

.settings-icon,
.logout-icon {
  font-size: 18px;
}

/* Responsive */
@media (max-width: 768px) {
  .sidebar {
    width: 70px;
  }

  .profile-info,
  .sidebar-header,
  .menu-label,
  .logout-btn span {
    display: none;
  }

  .user-profile,
  .menu-link,
  .logout-btn {
    justify-content: center;
  }

  .menu-icon-container {
    margin: 0;
  }

  .logout-icon-container {
    margin: 0;
  }
}

.sidebar.collapsed .profile-info,
.sidebar.collapsed .sidebar-header,
.sidebar.collapsed .menu-label,
.sidebar.collapsed .logout-btn span {
  display: none;
}

.sidebar.collapsed .user-profile,
.sidebar.collapsed .menu-link,
.sidebar.collapsed .logout-btn {
  justify-content: center;
}

.sidebar.collapsed .menu-icon-container {
  margin: 0;
}

.sidebar.collapsed .logout-icon-container {
  margin: 0;
}
