/* Premium Executive Sidebar - <PERSON>egant */
@import url('https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700;800&display=swap');

:root {
  --primary-dark: #0f172a;
  --primary-medium: #1e293b;
  --primary-light: #334155;
  --accent-gold: #f59e0b;
  --accent-orange: #ea580c;
  --accent-blue: #3b82f6;
  --white: #ffffff;
  --gray-50: #f8fafc;
  --gray-100: #f1f5f9;
  --gray-200: #e2e8f0;
  --gray-300: #cbd5e1;
  --gray-400: #94a3b8;
  --gray-500: #64748b;
  --text-primary: #0f172a;
  --text-secondary: #475569;
  --text-muted: #64748b;
  --border-subtle: rgba(148, 163, 184, 0.2);
  --shadow-elegant: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
  --shadow-soft: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
  --gradient-executive: linear-gradient(145deg, #0f172a 0%, #1e293b 50%, #334155 100%);
  --gradient-accent: linear-gradient(135deg, #f59e0b 0%, #ea580c 100%);
}

.sidebar {
  width: 280px;
  height: 100vh;
  position: fixed;
  top: 0;
  left: 0;
  background: var(--gradient-primary);
  font-family: 'Inter', sans-serif;
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  z-index: 1000;
  box-shadow: var(--shadow-lg);
  overflow: hidden;
  position: relative;
}

.sidebar::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background:
    radial-gradient(circle at 20% 20%, rgba(249, 115, 22, 0.1) 0%, transparent 50%),
    radial-gradient(circle at 80% 80%, rgba(59, 130, 246, 0.1) 0%, transparent 50%);
  pointer-events: none;
  z-index: 1;
}

.sidebar > * {
  position: relative;
  z-index: 2;
}

.sidebar.collapsed {
  width: 70px;
}

/* Toggle Button */
.toggle-btn {
  position: absolute;
  top: 24px;
  right: -18px;
  width: 36px;
  height: 36px;
  background: var(--gradient-orange);
  color: var(--white);
  border: none;
  border-radius: 50%;
  cursor: pointer;
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 8px 25px rgba(249, 115, 22, 0.4);
  z-index: 1001;
}

.toggle-btn:hover {
  transform: scale(1.15) rotate(180deg);
  box-shadow: 0 12px 35px rgba(249, 115, 22, 0.6);
}

.toggle-btn mat-icon {
  font-size: 20px;
  transition: all 0.3s ease;
}

/* User Profile */
.user-profile {
  display: flex;
  align-items: center;
  padding: 24px 20px;
  margin: 20px 16px 0;
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 16px;
  cursor: pointer;
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  overflow: hidden;
}

.user-profile::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.1), transparent);
  transition: 0.6s;
}

.user-profile:hover::before {
  left: 100%;
}

.user-profile:hover {
  transform: translateY(-2px);
  background: rgba(255, 255, 255, 0.15);
  border-color: var(--orange);
  box-shadow: 0 8px 25px rgba(249, 115, 22, 0.3);
}

.profile-avatar {
  position: relative;
  margin-right: 12px;
}

.avatar,
.avatar-placeholder {
  width: 40px;
  height: 40px;
  border-radius: 50%;
}

.avatar {
  object-fit: cover;
  border: 3px solid var(--orange);
  box-shadow: 0 4px 15px rgba(249, 115, 22, 0.4);
}

.avatar-placeholder {
  background: var(--gradient-orange);
  display: flex;
  align-items: center;
  justify-content: center;
  border: 3px solid rgba(255, 255, 255, 0.3);
}

.avatar-icon {
  font-size: 22px;
  color: var(--white);
}

.avatar-skeleton {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  background: var(--border-light);
  animation: pulse 1.5s infinite;
}

.status-indicator {
  position: absolute;
  bottom: 0;
  right: 0;
  width: 10px;
  height: 10px;
  background: #22c55e;
  border: 2px solid var(--white);
  border-radius: 50%;
}

.profile-info {
  flex: 1;
  transition: all 0.3s ease;
}

.user-name {
  font-size: 15px;
  font-weight: 600;
  color: var(--white);
  margin-bottom: 4px;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
}

.user-email {
  font-size: 12px;
  color: rgba(255, 255, 255, 0.8);
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
}

.skeleton {
  background: var(--border-light);
  border-radius: 4px;
  height: 14px;
  width: 100px;
  animation: pulse 1.5s infinite;
}

/* Sidebar Header */
.sidebar-header {
  padding: 24px 20px;
  margin: 16px 16px 24px;
  text-align: center;
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 16px;
  position: relative;
  overflow: hidden;
}

.sidebar-header::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 2px;
  background: var(--gradient-orange);
  animation: glow-line 3s ease-in-out infinite;
}

.logo {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 12px;
}

.logo-container {
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
}

.logo-icon {
  font-size: 36px;
  color: var(--orange);
  filter: drop-shadow(0 0 10px rgba(249, 115, 22, 0.5));
  animation: logo-pulse 3s ease-in-out infinite;
}

.dashboard-title {
  font-size: 20px;
  font-weight: 700;
  color: var(--white);
  margin: 0;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
}

.dashboard-subtitle {
  font-size: 11px;
  color: rgba(255, 255, 255, 0.8);
  margin: 0;
  text-transform: uppercase;
  letter-spacing: 0.8px;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
}

/* Navigation Menu */
.sidebar-nav {
  flex: 1;
  padding: 20px 0;
}

.sidebar-menu {
  list-style: none;
  padding: 0;
  margin: 0;
}

.sidebar-menu li {
  margin: 4px 0;
}

.menu-link {
  display: flex;
  align-items: center;
  padding: 16px 20px;
  color: rgba(255, 255, 255, 0.8);
  text-decoration: none;
  font-size: 14px;
  font-weight: 500;
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  border-radius: 12px;
  margin: 0 16px;
  overflow: hidden;
}

.menu-link::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.1), transparent);
  transition: 0.6s;
}

.menu-link:hover::before {
  left: 100%;
}

.menu-link:hover {
  background: rgba(255, 255, 255, 0.1);
  color: var(--white);
  transform: translateX(4px);
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
}

.sidebar-menu li.active .menu-link {
  background: var(--gradient-orange);
  color: var(--white);
  font-weight: 600;
  box-shadow: 0 8px 25px rgba(249, 115, 22, 0.4);
  transform: translateX(4px);
}

.menu-icon-container {
  margin-right: 12px;
  width: 20px;
  display: flex;
  justify-content: center;
}

.menu-icon {
  font-size: 20px;
  transition: all 0.3s ease;
}

.menu-label {
  flex: 1;
}

/* Sidebar Footer */
.sidebar-footer {
  padding: 20px 0 24px;
  position: relative;
}

.logout-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  width: calc(100% - 32px);
  margin: 0 16px;
  padding: 16px;
  background: rgba(220, 38, 38, 0.8);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(220, 38, 38, 0.3);
  color: var(--white);
  border-radius: 12px;
  font-size: 14px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  overflow: hidden;
}

.logout-btn::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transition: 0.6s;
}

.logout-btn:hover::before {
  left: 100%;
}

.logout-btn:hover {
  background: rgba(220, 38, 38, 1);
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(220, 38, 38, 0.4);
}

.logout-icon-container {
  margin-right: 8px;
}

.logout-icon {
  font-size: 18px;
}

/* Animations */
@keyframes pulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
}

@keyframes logo-pulse {
  0%, 100% {
    transform: scale(1);
    filter: drop-shadow(0 0 10px rgba(249, 115, 22, 0.5));
  }
  50% {
    transform: scale(1.05);
    filter: drop-shadow(0 0 20px rgba(249, 115, 22, 0.8));
  }
}

@keyframes glow-line {
  0%, 100% {
    opacity: 0.5;
    transform: scaleX(1);
  }
  50% {
    opacity: 1;
    transform: scaleX(1.05);
  }
}

/* Responsive */
@media (max-width: 768px) {
  .sidebar {
    width: 70px;
  }
  
  .profile-info,
  .sidebar-header,
  .menu-label,
  .logout-btn span {
    display: none;
  }
  
  .user-profile,
  .menu-link,
  .logout-btn {
    justify-content: center;
  }
  
  .menu-icon-container {
    margin: 0;
  }
  
  .logout-icon-container {
    margin: 0;
  }
}

.sidebar.collapsed .profile-info,
.sidebar.collapsed .sidebar-header,
.sidebar.collapsed .menu-label,
.sidebar.collapsed .logout-btn span {
  display: none;
}

.sidebar.collapsed .user-profile,
.sidebar.collapsed .menu-link,
.sidebar.collapsed .logout-btn {
  justify-content: center;
}

.sidebar.collapsed .menu-icon-container {
  margin: 0;
}

.sidebar.collapsed .logout-icon-container {
  margin: 0;
}
