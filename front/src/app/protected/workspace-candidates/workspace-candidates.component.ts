import { Component, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { MatCardModule } from '@angular/material/card';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { MatChipsModule } from '@angular/material/chips';
import { MatProgressSpinnerModule } from '@angular/material/progress-spinner';
import { Router } from '@angular/router';
import { JobApplicationService } from '../../services/job-application/job-application.service';
import { WorkspaceService } from '../../services/workspace/workspace.service';
import { PostService } from '../../services/post/post.service';

interface Candidate {
  id: string;
  name: string;
  email: string;
  phone?: string;
  linkedin?: string;
  cvFileName?: string;
  applicationDate: string;
  status: 'PENDING' | 'ACCEPTED' | 'REJECTED';
  postTitle?: string;
  postId?: string;
}

@Component({
  selector: 'app-workspace-candidates',
  standalone: true,
  imports: [
    CommonModule,
    MatCardModule,
    MatButtonModule,
    MatIconModule,
    MatChipsModule,
    MatProgressSpinnerModule
  ],
  templateUrl: './workspace-candidates.component.html',
  styleUrl: './workspace-candidates.component.css'
})
export class WorkspaceCandidatesComponent implements OnInit {
  candidates: Candidate[] = [];
  isLoading = true;
  workspaceName = '';
  totalCandidates = 0;
  pendingCandidates = 0;
  acceptedCandidates = 0;
  rejectedCandidates = 0;

  constructor(
    private jobApplicationService: JobApplicationService,
    private workspaceService: WorkspaceService,
    private postService: PostService,
    private router: Router
  ) {}

  ngOnInit() {
    this.loadWorkspaceCandidates();
  }

  loadWorkspaceCandidates() {
    this.isLoading = true;
    
    // D'abord, récupérer les informations du workspace actif
    this.workspaceService.getActiveWorkspaceInfo().subscribe({
      next: (workspace) => {
        this.workspaceName = workspace.name;
        
        // Ensuite, récupérer toutes les offres du workspace
        this.postService.getPosts().subscribe({
          next: (posts) => {
            this.loadCandidatesForAllPosts(posts);
          },
          error: (error) => {
            console.error('Erreur lors du chargement des offres:', error);
            this.isLoading = false;
          }
        });
      },
      error: (error) => {
        console.error('Erreur lors du chargement du workspace:', error);
        this.workspaceName = 'Workspace';
        this.isLoading = false;
      }
    });
  }

  loadCandidatesForAllPosts(posts: any[]) {
    const candidatePromises = posts.map(post => 
      this.jobApplicationService.getCandidates(post.id).toPromise().then(candidates => 
        candidates?.map(candidate => ({
          ...candidate,
          postTitle: post.title,
          postId: post.id
        })) || []
      ).catch(error => {
        console.error(`Erreur pour l'offre ${post.id}:`, error);
        return [];
      })
    );

    Promise.all(candidatePromises).then(results => {
      this.candidates = results.flat();
      this.calculateStats();
      this.isLoading = false;
    });
  }

  calculateStats() {
    this.totalCandidates = this.candidates.length;
    this.pendingCandidates = this.candidates.filter(c => c.status === 'PENDING').length;
    this.acceptedCandidates = this.candidates.filter(c => c.status === 'ACCEPTED').length;
    this.rejectedCandidates = this.candidates.filter(c => c.status === 'REJECTED').length;
  }

  getStatusLabel(status: string): string {
    switch (status) {
      case 'PENDING': return 'En attente';
      case 'ACCEPTED': return 'Accepté';
      case 'REJECTED': return 'Refusé';
      default: return status;
    }
  }

  getStatusClass(status: string): string {
    switch (status) {
      case 'PENDING': return 'status-pending';
      case 'ACCEPTED': return 'status-accepted';
      case 'REJECTED': return 'status-rejected';
      default: return '';
    }
  }

  acceptCandidate(candidateId: string) {
    this.jobApplicationService.acceptApplication(candidateId).subscribe({
      next: () => {
        const candidate = this.candidates.find(c => c.id === candidateId);
        if (candidate) {
          candidate.status = 'ACCEPTED';
          this.calculateStats();
        }
      },
      error: (error) => {
        console.error('Erreur lors de l\'acceptation:', error);
      }
    });
  }

  rejectCandidate(candidateId: string) {
    this.jobApplicationService.rejectApplication(candidateId).subscribe({
      next: () => {
        const candidate = this.candidates.find(c => c.id === candidateId);
        if (candidate) {
          candidate.status = 'REJECTED';
          this.calculateStats();
        }
      },
      error: (error) => {
        console.error('Erreur lors du refus:', error);
      }
    });
  }

  viewCandidateProfile(candidate: Candidate) {
    // Naviguer vers le profil du candidat
    this.router.navigate(['/candidate-profile', candidate.id]);
  }

  viewJobOffer(postId: string) {
    // Naviguer vers l'offre d'emploi
    this.router.navigate(['/post-detail', postId]);
  }

  shouldShowActionButtons(candidate: Candidate): boolean {
    return candidate.status === 'PENDING';
  }
}
