<div class="candidates-container">
  <!-- Header avec statistiques -->
  <div class="candidates-header">
    <div class="header-content">
      <div class="title-section">
        <h1 class="page-title">
          <mat-icon class="title-icon">people</mat-icon>
          Candidats du Workspace
        </h1>
        <p class="workspace-name">{{ workspaceName }}</p>
      </div>
      
      <div class="stats-cards" *ngIf="!isLoading">
        <div class="stat-card total">
          <div class="stat-number">{{ totalCandidates }}</div>
          <div class="stat-label">Total</div>
        </div>
        <div class="stat-card pending">
          <div class="stat-number">{{ pendingCandidates }}</div>
          <div class="stat-label">En attente</div>
        </div>
        <div class="stat-card accepted">
          <div class="stat-number">{{ acceptedCandidates }}</div>
          <div class="stat-label">Acceptés</div>
        </div>
        <div class="stat-card rejected">
          <div class="stat-number">{{ rejectedCandidates }}</div>
          <div class="stat-label">Refusés</div>
        </div>
      </div>
    </div>
  </div>

  <!-- Loading -->
  <div *ngIf="isLoading" class="loading-container">
    <mat-spinner></mat-spinner>
    <p>Chargement des candidats...</p>
  </div>

  <!-- Aucun candidat -->
  <div *ngIf="!isLoading && candidates.length === 0" class="no-candidates">
    <mat-icon class="no-candidates-icon">people_outline</mat-icon>
    <h3>Aucun candidat trouvé</h3>
    <p>Il n'y a pas encore de candidatures pour ce workspace.</p>
  </div>

  <!-- Liste des candidats -->
  <div *ngIf="!isLoading && candidates.length > 0" class="candidates-grid">
    <div *ngFor="let candidate of candidates" class="candidate-card">
      <!-- En-tête de la carte -->
      <div class="card-header">
        <div class="candidate-info">
          <h3 class="candidate-name" (click)="viewCandidateProfile(candidate)">
            {{ candidate.name }}
          </h3>
          <p class="candidate-email">{{ candidate.email }}</p>
        </div>
        <mat-chip [class]="getStatusClass(candidate.status)">
          {{ getStatusLabel(candidate.status) }}
        </mat-chip>
      </div>

      <!-- Informations du poste -->
      <div class="job-info" *ngIf="candidate.postTitle">
        <mat-icon class="job-icon">work</mat-icon>
        <span class="job-title" (click)="viewJobOffer(candidate.postId!)">
          {{ candidate.postTitle }}
        </span>
      </div>

      <!-- Détails du candidat -->
      <div class="candidate-details">
        <div class="detail-item" *ngIf="candidate.phone">
          <mat-icon class="detail-icon">phone</mat-icon>
          <span>{{ candidate.phone }}</span>
        </div>
        
        <div class="detail-item" *ngIf="candidate.linkedin">
          <mat-icon class="detail-icon">link</mat-icon>
          <a [href]="candidate.linkedin" target="_blank" class="linkedin-link">
            LinkedIn
          </a>
        </div>
        
        <div class="detail-item" *ngIf="candidate.cvFileName">
          <mat-icon class="detail-icon">description</mat-icon>
          <span class="cv-name">{{ candidate.cvFileName }}</span>
        </div>
        
        <div class="detail-item">
          <mat-icon class="detail-icon">event</mat-icon>
          <span>{{ candidate.applicationDate | date: 'dd/MM/yyyy' }}</span>
        </div>
      </div>

      <!-- Actions -->
      <div class="card-actions" *ngIf="shouldShowActionButtons(candidate)">
        <button mat-raised-button 
                class="accept-btn" 
                (click)="acceptCandidate(candidate.id)">
          <mat-icon>check_circle</mat-icon>
          Accepter
        </button>
        
        <button mat-raised-button 
                class="reject-btn" 
                (click)="rejectCandidate(candidate.id)">
          <mat-icon>cancel</mat-icon>
          Refuser
        </button>
      </div>
    </div>
  </div>
</div>
