/* Workspace Candidates - Beautiful Design */
.candidates-container {
  padding: 24px;
  background: #f8fafc;
  min-height: 100vh;
}

/* Header */
.candidates-header {
  background: white;
  border-radius: 16px;
  padding: 32px;
  margin-bottom: 24px;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
}

.header-content {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  gap: 32px;
}

.title-section {
  flex: 1;
}

.page-title {
  display: flex;
  align-items: center;
  gap: 12px;
  font-size: 28px;
  font-weight: 700;
  color: #1e3a8a;
  margin: 0 0 8px 0;
}

.title-icon {
  font-size: 32px;
  color: #f97316;
}

.workspace-name {
  font-size: 16px;
  color: #6b7280;
  margin: 0;
  font-weight: 500;
}

/* Stats Cards */
.stats-cards {
  display: flex;
  gap: 16px;
}

.stat-card {
  background: white;
  border-radius: 12px;
  padding: 20px;
  text-align: center;
  min-width: 100px;
  border: 2px solid transparent;
  transition: all 0.3s ease;
}

.stat-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
}

.stat-card.total {
  border-color: #1e3a8a;
}

.stat-card.pending {
  border-color: #f59e0b;
}

.stat-card.accepted {
  border-color: #10b981;
}

.stat-card.rejected {
  border-color: #ef4444;
}

.stat-number {
  font-size: 24px;
  font-weight: 700;
  margin-bottom: 4px;
}

.stat-card.total .stat-number {
  color: #1e3a8a;
}

.stat-card.pending .stat-number {
  color: #f59e0b;
}

.stat-card.accepted .stat-number {
  color: #10b981;
}

.stat-card.rejected .stat-number {
  color: #ef4444;
}

.stat-label {
  font-size: 12px;
  color: #6b7280;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

/* Loading */
.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 64px;
  text-align: center;
}

.loading-container p {
  margin-top: 16px;
  color: #6b7280;
  font-size: 16px;
}

/* No candidates */
.no-candidates {
  background: white;
  border-radius: 16px;
  padding: 64px;
  text-align: center;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
}

.no-candidates-icon {
  font-size: 64px;
  color: #d1d5db;
  margin-bottom: 16px;
}

.no-candidates h3 {
  color: #1f2937;
  margin: 0 0 8px 0;
  font-size: 20px;
  font-weight: 600;
}

.no-candidates p {
  color: #6b7280;
  margin: 0;
  font-size: 16px;
}

/* Candidates Grid */
.candidates-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(400px, 1fr));
  gap: 24px;
}

/* Candidate Card */
.candidate-card {
  background: white;
  border-radius: 16px;
  padding: 24px;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
  border: 1px solid #e5e7eb;
}

.candidate-card:hover {
  transform: translateY(-4px);
  box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1);
  border-color: #f97316;
}

/* Card Header */
.card-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 16px;
}

.candidate-info {
  flex: 1;
}

.candidate-name {
  font-size: 18px;
  font-weight: 600;
  color: #1e3a8a;
  margin: 0 0 4px 0;
  cursor: pointer;
  transition: color 0.3s ease;
}

.candidate-name:hover {
  color: #f97316;
}

.candidate-email {
  font-size: 14px;
  color: #6b7280;
  margin: 0;
}

/* Status Chips */
.status-pending {
  background-color: #fef3c7 !important;
  color: #92400e !important;
}

.status-accepted {
  background-color: #d1fae5 !important;
  color: #065f46 !important;
}

.status-rejected {
  background-color: #fee2e2 !important;
  color: #991b1b !important;
}

/* Job Info */
.job-info {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 16px;
  padding: 12px;
  background: #f8fafc;
  border-radius: 8px;
}

.job-icon {
  font-size: 18px;
  color: #f97316;
}

.job-title {
  font-weight: 500;
  color: #1e3a8a;
  cursor: pointer;
  transition: color 0.3s ease;
}

.job-title:hover {
  color: #f97316;
}

/* Candidate Details */
.candidate-details {
  display: flex;
  flex-direction: column;
  gap: 8px;
  margin-bottom: 20px;
}

.detail-item {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 14px;
  color: #4b5563;
}

.detail-icon {
  font-size: 16px;
  color: #9ca3af;
  width: 16px;
}

.linkedin-link {
  color: #1e3a8a;
  text-decoration: none;
  transition: color 0.3s ease;
}

.linkedin-link:hover {
  color: #f97316;
}

.cv-name {
  color: #1e3a8a;
  font-weight: 500;
}

/* Card Actions */
.card-actions {
  display: flex;
  gap: 12px;
  padding-top: 16px;
  border-top: 1px solid #e5e7eb;
}

.accept-btn {
  background: #10b981 !important;
  color: white !important;
  flex: 1;
  font-weight: 600 !important;
}

.accept-btn:hover {
  background: #059669 !important;
}

.reject-btn {
  background: #ef4444 !important;
  color: white !important;
  flex: 1;
  font-weight: 600 !important;
}

.reject-btn:hover {
  background: #dc2626 !important;
}

.accept-btn mat-icon,
.reject-btn mat-icon {
  margin-right: 8px;
  font-size: 18px;
}

/* Responsive */
@media (max-width: 768px) {
  .candidates-container {
    padding: 16px;
  }
  
  .header-content {
    flex-direction: column;
    gap: 24px;
  }
  
  .stats-cards {
    flex-wrap: wrap;
    justify-content: center;
  }
  
  .candidates-grid {
    grid-template-columns: 1fr;
  }
  
  .card-actions {
    flex-direction: column;
  }
}
