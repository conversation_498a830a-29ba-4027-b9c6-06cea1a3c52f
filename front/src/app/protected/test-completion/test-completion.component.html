<div class="completion-page">
  <div class="completion-container">
    <mat-card class="completion-card">
      <mat-card-header class="completion-header">
        <div class="header-icon" [style.color]="getScoreColor()">
          <mat-icon>{{ getScoreIcon() }}</mat-icon>
        </div>
        <mat-card-title>{{ getScoreMessage() }}</mat-card-title>
      </mat-card-header>

      <mat-card-content class="completion-content">
        <div class="message-section">
          <p class="main-message">{{ message }}</p>
        </div>

        <div *ngIf="showScore" class="score-section">
          <div class="score-display" [style.color]="getScoreColor()">
            <div class="score-circle" [style.border-color]="getScoreColor()">
              <span class="percentage">{{ percentage }}</span>
            </div>
            <div class="score-details">
              <p class="score-text">Score: {{ score }}</p>
              <p class="score-status">{{ getScoreMessage() }}</p>
            </div>
          </div>
        </div>

        <div class="actions-section">
          <button mat-raised-button color="primary" class="primary-button" (click)="goToWelcome()">
            <mat-icon>home</mat-icon>
            Retour à l'accueil
          </button>
          
          <button mat-button class="secondary-button" (click)="viewResults()">
            <mat-icon>assessment</mat-icon>
            Voir tous les résultats
          </button>
        </div>
      </mat-card-content>
    </mat-card>
  </div>
</div>
