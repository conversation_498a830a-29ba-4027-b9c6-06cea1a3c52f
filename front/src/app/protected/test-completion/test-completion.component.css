/* ==========================================================================
   Page de completion de test
   ========================================================================== */
.completion-page {
  min-height: 100vh;
  background: linear-gradient(135deg, #001040FF 0%, #001660FF 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 24px;
}

.completion-container {
  width: 100%;
  max-width: 600px;
}

/* ==========================================================================
   Carte principale
   ========================================================================== */
.completion-card {
  background: #FFFFFF;
  border-radius: 16px;
  box-shadow: 0 20px 40px rgba(0, 16, 64, 0.15);
  overflow: hidden;
  animation: slideUp 0.6s ease-out;
}

@keyframes slideUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* ==========================================================================
   En-tête
   ========================================================================== */
.completion-header {
  text-align: center;
  padding: 32px 24px 16px;
  background: linear-gradient(135deg, #F8FAFC 0%, #F1F5F9 100%);
}

.header-icon {
  margin-bottom: 16px;
}

.header-icon mat-icon {
  font-size: 64px;
  width: 64px;
  height: 64px;
  animation: bounce 1s ease-in-out;
}

@keyframes bounce {
  0%, 20%, 50%, 80%, 100% {
    transform: translateY(0);
  }
  40% {
    transform: translateY(-10px);
  }
  60% {
    transform: translateY(-5px);
  }
}

.completion-header mat-card-title {
  font-size: 28px;
  font-weight: 700;
  color: #1E293B;
  margin: 0;
  letter-spacing: -0.02em;
}

/* ==========================================================================
   Contenu
   ========================================================================== */
.completion-content {
  padding: 32px 24px;
}

.message-section {
  text-align: center;
  margin-bottom: 32px;
}

.main-message {
  font-size: 18px;
  color: #475569;
  line-height: 1.6;
  margin: 0;
}

/* ==========================================================================
   Section score
   ========================================================================== */
.score-section {
  margin-bottom: 32px;
}

.score-display {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 24px;
  padding: 24px;
  background: #F8FAFC;
  border-radius: 12px;
  border: 2px solid #E2E8F0;
}

.score-circle {
  width: 120px;
  height: 120px;
  border: 4px solid;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  background: #FFFFFF;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  animation: scaleIn 0.8s ease-out 0.3s both;
}

@keyframes scaleIn {
  from {
    opacity: 0;
    transform: scale(0.5);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}

.percentage {
  font-size: 24px;
  font-weight: 700;
  color: inherit;
}

.score-details {
  text-align: left;
}

.score-text {
  font-size: 20px;
  font-weight: 600;
  color: inherit;
  margin: 0 0 8px 0;
}

.score-status {
  font-size: 16px;
  color: #64748B;
  margin: 0;
}

/* ==========================================================================
   Actions
   ========================================================================== */
.actions-section {
  display: flex;
  flex-direction: column;
  gap: 16px;
  align-items: center;
}

.primary-button {
  background-color: #001040FF !important;
  color: white !important;
  padding: 12px 32px !important;
  font-size: 16px !important;
  font-weight: 600 !important;
  border-radius: 8px !important;
  min-width: 200px;
  transition: all 0.3s ease;
}

.primary-button:hover {
  background-color: #001660FF !important;
  transform: translateY(-2px);
  box-shadow: 0 8px 20px rgba(0, 16, 64, 0.3);
}

.primary-button mat-icon {
  margin-right: 8px;
}

.secondary-button {
  color: #001040FF !important;
  padding: 8px 24px !important;
  font-size: 14px !important;
  border-radius: 6px !important;
  transition: all 0.3s ease;
}

.secondary-button:hover {
  background-color: #F1F5F9 !important;
  color: #001660FF !important;
}

.secondary-button mat-icon {
  margin-right: 8px;
  font-size: 18px;
  width: 18px;
  height: 18px;
}

/* ==========================================================================
   Responsive
   ========================================================================== */
@media (max-width: 768px) {
  .completion-page {
    padding: 16px;
  }

  .completion-header {
    padding: 24px 16px 12px;
  }

  .completion-header mat-card-title {
    font-size: 24px;
  }

  .completion-content {
    padding: 24px 16px;
  }

  .score-display {
    flex-direction: column;
    gap: 16px;
    text-align: center;
  }

  .score-circle {
    width: 100px;
    height: 100px;
  }

  .percentage {
    font-size: 20px;
  }

  .score-details {
    text-align: center;
  }

  .actions-section {
    gap: 12px;
  }

  .primary-button {
    min-width: 180px;
    padding: 10px 24px !important;
    font-size: 14px !important;
  }
}

@media (max-width: 480px) {
  .header-icon mat-icon {
    font-size: 48px;
    width: 48px;
    height: 48px;
  }

  .completion-header mat-card-title {
    font-size: 20px;
  }

  .main-message {
    font-size: 16px;
  }

  .score-circle {
    width: 80px;
    height: 80px;
  }

  .percentage {
    font-size: 18px;
  }

  .score-text {
    font-size: 18px;
  }
}
