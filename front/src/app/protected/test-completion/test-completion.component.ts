import { Component, OnInit } from '@angular/core';
import { ActivatedRoute, Router } from '@angular/router';
import { <PERSON><PERSON>ard, Mat<PERSON>ard<PERSON>ontent, MatCardHeader, MatCardTitle } from '@angular/material/card';
import { MatButton } from '@angular/material/button';
import { MatIcon } from '@angular/material/icon';
import { NgIf } from '@angular/common';

@Component({
  selector: 'app-test-completion',
  standalone: true,
  imports: [
    Mat<PERSON>ard,
    MatCardContent,
    MatCardHeader,
    MatCardTitle,
    MatButton,
    MatIcon,
    NgIf
  ],
  templateUrl: './test-completion.component.html',
  styleUrls: ['./test-completion.component.css']
})
export class TestCompletionComponent implements OnInit {
  score: string = '';
  percentage: string = '';
  message: string = '';
  showScore: boolean = false;

  constructor(
    private route: ActivatedRoute,
    private router: Router
  ) {}

  ngOnInit() {
    // Récupérer les paramètres de la route
    this.route.queryParams.subscribe(params => {
      this.score = params['score'] || '';
      this.percentage = params['percentage'] || '';
      this.message = params['message'] || 'Merci d\'avoir passé ce test !';
      this.showScore = !!(this.score && this.percentage);
    });
  }

  getScoreColor(): string {
    if (!this.percentage) return '#6B7280';
    
    const numericPercentage = parseInt(this.percentage.replace('%', ''));
    if (numericPercentage >= 80) return '#22C55E'; // Vert
    if (numericPercentage >= 60) return '#F59E0B'; // Orange
    return '#EF4444'; // Rouge
  }

  getScoreIcon(): string {
    if (!this.percentage) return 'assignment_turned_in';
    
    const numericPercentage = parseInt(this.percentage.replace('%', ''));
    if (numericPercentage >= 80) return 'emoji_events'; // Trophée
    if (numericPercentage >= 60) return 'thumb_up'; // Pouce levé
    return 'trending_up'; // Graphique montant
  }

  getScoreMessage(): string {
    if (!this.percentage) return 'Test terminé avec succès';
    
    const numericPercentage = parseInt(this.percentage.replace('%', ''));
    if (numericPercentage >= 80) return 'Excellent travail !';
    if (numericPercentage >= 60) return 'Bon travail !';
    if (numericPercentage >= 40) return 'Résultat correct';
    return 'Continuez vos efforts';
  }

  goToWelcome() {
    this.router.navigate(['/welcome']);
  }

  viewResults() {
    this.router.navigate(['/testResults']);
  }
}
