import { Injectable } from '@angular/core';
import { HttpClient, HttpHeaders } from '@angular/common/http';
import { Observable, throwError } from 'rxjs';
import { map, catchError } from 'rxjs/operators';

export interface TestSettings {
  level: string;
  questionCount: number;
  testType: string;
  duration: number;
}

export interface TestGenerationRequest {
  settings: TestSettings;
  candidateId?: string;
  candidateProfile?: any;
  jobRequirements?: any;
}

export interface GeneratedTest {
  id: string;
  title: string;
  questions: Question[];
  settings: TestSettings;
  createdAt: Date;
}

export interface Question {
  content: string;
  type: 'RADIO' | 'SELECT' | 'CODE';
  options?: string[];
  correctOptions?: number[];
  codeTemplate?: string;
}

@Injectable({
  providedIn: 'root'
})
export class TestGenerationService {
  private apiUrl = 'http://localhost:8080/api'; // URL de votre backend Spring Boot

  constructor(private http: HttpClient) {}

  /**
   * Génère un test personnalisé avec les paramètres spécifiés
   */
  generateCustomTest(testParameters: any): Observable<any> {
    const headers = new HttpHeaders({
      'Content-Type': 'application/json',
      'Skip-Auth': 'true' // Indique à l'intercepteur de ne pas ajouter l'authentification
    });

    console.log('🤖 Envoi de la demande de génération de test personnalisé:', testParameters);

    return this.http.post<any>(`${this.apiUrl}/tests/generate-custom`, testParameters, { headers })
      .pipe(
        map(response => {
          console.log('✅ Test personnalisé généré:', response);
          return {
            id: response.id || this.generateTestId(),
            title: response.name || response.title || 'Test généré',
            questions: response.questions || [],
            settings: {
              level: testParameters.level,
              questionCount: testParameters.questionCount,
              testType: testParameters.testType,
              duration: testParameters.duration
            },
            createdAt: new Date()
          };
        }),
        catchError(error => {
          console.error('❌ Erreur lors de la génération du test personnalisé:', error);
          return throwError(() => error);
        })
      );
  }

  /**
   * Génère un test avec l'IA en utilisant les paramètres sélectionnés
   */
  generateTestWithAI(settings: TestSettings): Observable<GeneratedTest> {
    const headers = new HttpHeaders({
      'Content-Type': 'application/json'
    });

    // Créer le prompt basé sur les paramètres
    const prompt = this.createPromptFromSettings(settings);

    console.log('🤖 Envoi de la demande de génération de test à l\'IA:', settings);
    console.log('📝 Prompt généré:', prompt);

    // Utiliser l'endpoint /tests/generate pour générer le test
    const requestBody = {
      level: settings.level,
      questionCount: settings.questionCount,
      testType: settings.testType,
      duration: settings.duration,
      skill: this.getSubjectFromSettings(settings)
    };

    return this.http.post<any>(`${this.apiUrl}/tests/generate-custom`, requestBody, { headers })
      .pipe(
        map(response => {
          console.log('✅ Test généré:', response);
          return this.transformBackendResponse(response, settings);
        }),
        catchError(error => {
          console.error('❌ Erreur lors de la génération:', error);
          return throwError(() => error);
        })
      );
  }

  /**
   * Alternative: Génère un test en utilisant l'endpoint /tests/generate
   */
  generateTestAlternative(settings: TestSettings): Observable<GeneratedTest> {
    const headers = new HttpHeaders({
      'Content-Type': 'application/json'
    });

    const skill = this.getSubjectFromSettings(settings);

    console.log('🤖 Génération alternative avec skill:', skill);

    return this.http.post<any>(`${this.apiUrl}/tests/generate`, skill, {
      headers: {
        'Content-Type': 'text/plain'
      }
    })
      .pipe(
        map(response => {
          console.log('✅ Test généré (alternative):', response);
          return this.transformBackendResponse(response, settings);
        }),
        catchError(error => {
          console.error('❌ Erreur lors de la génération alternative:', error);
          return throwError(() => error);
        })
      );
  }

  /**
   * Crée un prompt personnalisé basé sur les paramètres
   */
  private createPromptFromSettings(settings: TestSettings): string {
    const levelText = this.getLevelText(settings.level);
    const typeText = this.getTypeText(settings.testType);
    const durationText = this.getDurationText(settings.duration);

    return `
Génère un test technique personnalisé avec les caractéristiques suivantes:

- Niveau: ${settings.level} (${levelText})
- Nombre de questions: ${settings.questionCount}
- Type: ${settings.testType} (${typeText})
- Durée estimée: ${durationText}

Le test doit être au format JSON avec la structure:
{
  "title": "Titre du test",
  "questions": [
    {
      "content": "Question...",
      "type": "${settings.testType === 'code' ? 'CODE' : 'RADIO'}",
      "options": ["Option 1", "Option 2", "Option 3", "Option 4"],
      "correctOptions": [0]
    }
  ]
}

Assure-toi que:
- Les questions sont adaptées au niveau ${settings.level}
- Le test peut être complété en ${durationText}
- Chaque question a au moins 4 options de réponse
- Les questions sont variées et progressives
    `.trim();
  }

  /**
   * Transforme la réponse du backend en format standardisé
   */
  private transformBackendResponse(response: any, settings: TestSettings): GeneratedTest {
    return {
      id: response.id || this.generateTestId(),
      title: response.name || response.title || `Test ${settings.testType} - ${settings.level}`,
      questions: response.questions || [],
      settings: settings,
      createdAt: new Date()
    };
  }

  /**
   * Détermine le sujet basé sur les paramètres
   */
  private getSubjectFromSettings(settings: TestSettings): string {
    // Vous pouvez personnaliser ceci selon vos besoins
    if (settings.testType === 'code') {
      return 'programmation';
    } else if (settings.testType === 'qcm') {
      return 'connaissances techniques';
    } else {
      return 'développement logiciel';
    }
  }

  /**
   * Textes pour les niveaux
   */
  private getLevelText(level: string): string {
    const levels = {
      'debutant': 'Questions de base et concepts fondamentaux',
      'intermediaire': 'Concepts avancés et applications pratiques',
      'expert': 'Défis complexes et expertise approfondie'
    };
    return levels[level as keyof typeof levels] || level;
  }

  /**
   * Textes pour les types
   */
  private getTypeText(type: string): string {
    const types = {
      'qcm': 'Questions à choix multiples',
      'code': 'Exercices de programmation',
      'mixte': 'QCM et exercices pratiques'
    };
    return types[type as keyof typeof types] || type;
  }

  /**
   * Textes pour les durées
   */
  private getDurationText(duration: number): string {
    if (duration < 60) {
      return `${duration} minutes`;
    } else {
      const hours = Math.floor(duration / 60);
      const minutes = duration % 60;
      return minutes > 0 ? `${hours}h${minutes}min` : `${hours}h`;
    }
  }

  /**
   * Génère un ID unique pour le test
   */
  private generateTestId(): string {
    return `test_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }
}
