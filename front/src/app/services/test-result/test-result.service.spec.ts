import { TestBed } from '@angular/core/testing';
import { HttpClientTestingModule, HttpTestingController } from '@angular/common/http/testing';
import { OidcSecurityService } from 'angular-auth-oidc-client';
import { of } from 'rxjs';

import { TestResultService, TestResultWithScore } from './test-result.service';
import { environment } from '../../../environments/environment';

describe('TestResultService', () => {
  let service: TestResultService;
  let httpMock: HttpTestingController;
  let oidcServiceSpy: jasmine.SpyObj<OidcSecurityService>;

  beforeEach(() => {
    const spy = jasmine.createSpyObj('OidcSecurityService', ['getAccessToken']);

    TestBed.configureTestingModule({
      imports: [HttpClientTestingModule],
      providers: [
        TestResultService,
        { provide: OidcSecurityService, useValue: spy }
      ]
    });

    service = TestBed.inject(TestResultService);
    httpMock = TestBed.inject(HttpTestingController);
    oidcServiceSpy = TestBed.inject(OidcSecurityService) as jasmine.SpyObj<OidcSecurityService>;
  });

  afterEach(() => {
    httpMock.verify();
  });

  it('should be created', () => {
    expect(service).toBeTruthy();
  });

  it('should get all test results', () => {
    const mockToken = 'mock-token';
    const mockResults: TestResultWithScore[] = [
      {
        id: '1',
        testId: 'test1',
        userId: 'user1',
        candidateName: 'John Doe',
        testName: 'JavaScript Test',
        submittedAt: new Date(),
        totalQuestions: 10,
        correctAnswers: 8,
        scorePercentage: 80
      }
    ];

    oidcServiceSpy.getAccessToken.and.returnValue(of(mockToken));

    service.getAllTestResults().subscribe(results => {
      expect(results).toEqual(mockResults);
    });

    const req = httpMock.expectOne(`${environment.apiUrl}/tests/results`);
    expect(req.request.method).toBe('GET');
    expect(req.request.headers.get('Authorization')).toBe(`Bearer ${mockToken}`);
    req.flush(mockResults);
  });

  it('should transform data to display format', () => {
    const mockResults: TestResultWithScore[] = [
      {
        id: '1',
        testId: 'test1',
        userId: 'user1',
        candidateName: 'John Doe',
        testName: 'JavaScript Test',
        submittedAt: new Date('2024-01-15'),
        totalQuestions: 10,
        correctAnswers: 8,
        scorePercentage: 80
      }
    ];

    const displayResults = service.transformToDisplayFormat(mockResults);

    expect(displayResults).toEqual([
      {
        id: '1',
        job: 'JavaScript Test',
        name: 'John Doe',
        date: '15/01/2024',
        timeLimit: '1h',
        timePassed: 'N/A',
        score: '80%',
        correctAnswers: 8,
        totalQuestions: 10
      }
    ]);
  });

  it('should return correct score color', () => {
    expect(service.getScoreColor(90)).toBe('#22C55E'); // Vert
    expect(service.getScoreColor(70)).toBe('#F59E0B'); // Orange
    expect(service.getScoreColor(30)).toBe('#EF4444'); // Rouge
  });

  it('should return correct score status', () => {
    expect(service.getScoreStatus(90)).toBe('Excellent');
    expect(service.getScoreStatus(70)).toBe('Bien');
    expect(service.getScoreStatus(50)).toBe('Moyen');
    expect(service.getScoreStatus(30)).toBe('Faible');
  });
});
