// authInterceptor.ts
import {inject} from '@angular/core';
import {HttpEvent, HttpHandlerFn, HttpRequest} from '@angular/common/http';
import {mergeMap, Observable} from 'rxjs';
import {OidcSecurityService} from 'angular-auth-oidc-client';
import {environment} from '../environments/environment';
import {WorkspaceService} from './services/workspace/workspace.service';

export function addOauthIntercept(req: HttpRequest<any>, next: HttpHandlerFn): Observable<HttpEvent<any>> {

  // Check if the request should skip authentication
  if (req.headers.has('Skip-Auth')) {
    // Remove the Skip-Auth header and proceed without authentication
    const clonedRequest = req.clone({
      headers: req.headers.delete('Skip-Auth')
    });
    return next(clonedRequest);
  }

  if (req.url.startsWith(environment.apiUrl)) {
    const oidcSecurityService = inject(OidcSecurityService);
    const workspaceService = inject(WorkspaceService);
    return oidcSecurityService.getAccessToken().pipe(mergeMap(accessToken => {
      if (accessToken) {
        const clonedRequest = req.clone({
          setHeaders: {
            Authorization: `Bearer ${accessToken}`,
            'WWW-Workspace-Id': workspaceService.getCurrentWorkspaceId()
          },
        });

        return next(clonedRequest);
      }

      return next(req);
    }));

  }
  return next(req);
}
